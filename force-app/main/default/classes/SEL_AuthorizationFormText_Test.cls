/**
 * @description Test class for SEL_AuthorizationForm
 *
 * <AUTHOR> (<EMAIL>)
 * @date September 2024
 */
@isTest
private class SEL_AuthorizationFormText_Test {

    @IsTest
    static void shouldSelectById() {
        Test.startTest();
        SEL_AuthorizationFormText.newInstance().selectById(new Set<Id>());
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Id IN :idSet'), 'Condition used id set.');
    }
}