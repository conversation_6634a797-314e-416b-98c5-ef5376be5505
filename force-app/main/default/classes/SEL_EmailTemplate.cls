/**
 * @description       : US: SFP-11298 Add to calendar from email functionality
 * <AUTHOR> TCK
 * @group             :
 * @last modified on  : 06-14-2022
 * @last modified by  : TCK
 **/
public without sharing class SEL_EmailTemplate extends fflib_SObjectSelector {
    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @return List<Schema.SObjectField>
     **/
    public List<Schema.SObjectField> getSObjectFieldList() {
        return new List<Schema.SObjectField>{
            EmailTemplate.Id,
            EmailTemplate.Subject,
            EmailTemplate.Description,
            EmailTemplate.HtmlValue,
            EmailTemplate.DeveloperName,
            EmailTemplate.Body
        };
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @return Schema.SObjectType
     **/
    public Schema.SObjectType getSObjectType() {
        return EmailTemplate.sObjectType;
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @return SEL_EmailTemplate
     **/
    public static SEL_EmailTemplate newInstance() {
        return (SEL_EmailTemplate) ORG_Application.selector.newInstance(
            EmailTemplate.SObjectType
        );
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @param name
     * @return List<EmailTemplate>
     **/
    public List<EmailTemplate> selectByName(String name) {
        return (List<EmailTemplate>) Database.query(
            newQueryFactory().setCondition('Name =: name').toSOQL()
        );
    }
}
