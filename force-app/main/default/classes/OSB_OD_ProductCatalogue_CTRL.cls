/**
 * @description a controller class for the product catalogue page on OneHub
 *
 * <AUTHOR> (<EMAIL>)
 * @date August 2022
 *
 * @LastModified July 2024
 * <AUTHOR> (<EMAIL>)
 * @UserStory SFP-40359
 * @LastModifiedReason Removal of methods generateAssetVersionId and getAnypointPages. Update createProductMap for Icon being stored in ContentDocuments
 *
 */
public without sharing class OSB_OD_ProductCatalogue_CTRL {

    private static final sbgplatform.rflib_Logger LOGGER = sbgplatform.rflib_LoggerUtil.getFactory().createLogger('OSB_OD_ProductCatalogue_CTRL');

    /**
     * @description returns a product map that contains the product info as well as the category
     *
     * @return a list of product maps that contain product info and category
     */
    @AuraEnabled(cacheable=true)
    public static List<Map<string, string>> getProductMap() {

        List<Map<string, string>> result = new List<Map<string, string>>();
        Map<string, Map<string, string>> productIdMap = new Map<string, Map<string, string>>();
        Set<Id> productIdSet = new Set<Id>();

        try {

            List<acm_pkg__CommunityApi__c> products = getProducts();
            Set<acm_pkg__CommunityApi__c> productSet = new Set<acm_pkg__CommunityApi__c>();
            productSet.addAll(products);
            Set<string> productAnypointIds = getAnypointIds(products);
            for (acm_pkg__CommunityApi__c p : products) {
                productIdSet.add(p.Id);
            }
    
            List<acm_pkg__AnypointAssetCategories__x> categories = getCategories(
                productAnypointIds
            );
            for (acm_pkg__CommunityApi__c p : products) {
                Map<string, string> productMap = createProductMap(p);
                productIdMap.put(p.acm_pkg__ApiId__c, productMap);
            }
    
            for (acm_pkg__AnypointAssetCategories__x c : categories) {
                if (productIdMap.containsKey(c.acm_pkg__AssetId__c)) {
                    Map<string, string> product = productIdMap.get(
                        c.acm_pkg__AssetId__c
                    );
                    productIdMap.remove(c.acm_pkg__AssetId__c);
                    updateMapKey(
                        product,
                        'Category',
                        c.acm_pkg__Category__c.split(':')[1]
                    );
                    result.add(product);
                }
            }
            
        } catch (Exception e) {
            LOGGER.error('OSB_OD_ProductCatalogue_CTRL : getProductMap  Retrieve API products to OneDeveloper to Exception logged: ', e);
        }

        return result;
    }

    /**
     * @description a helper method that fetches a products categories
     *
     * @param assetIds a list of AnyPoint asset Id's
     * @return a list of multiple product's categories
     */
    private static List<acm_pkg__AnypointAssetCategories__x> getCategories(
        Set<string> assetIds
    ) {
        return SEL_AnypointCategories.newInstance().selectByAssetIdAndDisplayName(assetIds, 'Category');
    }

    /**
     * @description returns a list of api products that exist in OneHub
     *
     * @return a list of api products that exist in OneHub
     */
    private static List<acm_pkg__CommunityApi__c> getProducts() {
        return SEL_CommunityApis.newInstance().selectByAssetTypeAndCommunity('api-group', 'OneHub');
    }

    /**
     * @description returns a set of acm_pkg__ApiId__c
     *
     * @param products a list of acm_pkg__CommunityApi__c
     *
     * @return a set of ids as string
     */
    private static Set<string> getAnypointIds(
        List<acm_pkg__CommunityApi__c> products
    ) {
        Set<string> productAnypointIds = new Set<string>();
        for (acm_pkg__CommunityApi__c p : products) {
            productAnypointIds.add(p.acm_pkg__ApiId__c);
        }
        return productAnypointIds;
    }

    /**
     * @description returns a map of products
     *
     * @param product a acm_pkg__CommunityApi__c object
     *
     * @return a map of product objects
     */
    private static Map<string, string> createProductMap(
        acm_pkg__CommunityApi__c product
    ) {
        Map<string, string> productMap = createEmptyProductMap();
        updateMapKey(productMap, 'Id', product.Id);
        updateMapKey(productMap, 'Name', product.acm_pkg__Name__c);
        if (product.acm_pkg__Description__c != null) {
            updateMapKey(
                productMap,
                'Description',
                product.acm_pkg__Description__c
            );
        }
        if (product.acm_pkg__Icon__c != null) {
            updateMapKey(
                productMap,
                'Icon',
                '/sfc/servlet.shepherd/document/download/' +
                product.acm_pkg__Icon__c
            );
        }
        return productMap;
    }

    /**
     * @description a helper method that creates the product map
     *
     * @return an empty map template for a product map
     */
    private static Map<string, string> createEmptyProductMap() {
        return new Map<string, string>{
            'Id' => '',
            'AssetVersionId' => '',
            'Name' => '',
            'Description' => '',
            'Icon' => '',
            'Category' => '',
            'IsExternal' => ''
        };
    }

    /**
     * @description safely updates a map by providing the key value pair
     *
     * @param inputMap a input map
     * @param key a map key
     * @param newValue a new value
     * @return if the map contains the key it will return the updated map
     */
    private static Map<string, string> updateMapKey(
        Map<string, string> inputMap,
        string key,
        string newValue
    ) {
        if (inputMap.containsKey(key)) {
            inputMap.put(key, newValue);
            return inputMap;
        }
        return null;
    }
}
