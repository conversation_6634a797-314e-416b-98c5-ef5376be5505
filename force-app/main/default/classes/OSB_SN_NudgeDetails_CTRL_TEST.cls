/**
 * @description Test class for OSB_SN_NudgeDetails_CTRL
 * <AUTHOR>
 * @date July 2023
 */
@isTest
public class OSB_SN_NudgeDetails_CTRL_TEST{

    /**
     * @description  getInsightData retrieve the relevant data for all cases
     */
    @isTest
    static void getNudgeTest(){
        List<Insight__c> ins;
        
        List<Insight__c> searchIns;
        List<Insight__c> myIns = AKI_TESTDATA.createInsights(5);
        Test.startTest();
        OSB_SN_NudgeDetails_CTRL nudgeDetailsCTRL = new OSB_SN_NudgeDetails_CTRL();
        nudgeDetailsCTRL.nudgeRecId = myIns[0].Id;
        nudgeDetailsCTRL.getHeaderImageURL();
        nudgeDetailsCTRL.getNudge();
        nudgeDetailsCTRL.getSmartNudgeSummaryImageURL();
        Test.stopTest();
        System.assert(myIns.size() >0, 'Was expecting to find at least one insight');
    }

}