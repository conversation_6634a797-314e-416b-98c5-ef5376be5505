/**
 * @description This is a rest service class to allow for OneHub Solutions to onboard a contact
 *
 * <AUTHOR>
 * @description Rest Service Class for OneHub Contact Onboarding
 * @UserStory SFP-41159
 * @date March 2024
 *
 */
@RestResource(urlMapping='/onehub-users/*')
global with sharing class OSB_API_Contact_Onboarding {
    private static final sbgplatform.rflib_Logger LOGGER = sbgplatform.rflib_LoggerUtil.getFactory()
        .createLogger('OSB_API_Contact_Onboarding');
    public static final string OSB_MAIL_ENCRKEY = 'EMAIL_ENCR';
    public static final String OSB_CUSTOM_SETTING_OSB_BASE_URL = 'OSB_Base_URL';
    @TestVisible
    private static final String ACCOUNT_DOES_NOT_EXIST = 'Account_does_not_exist';
    @TestVisible
    private static final String MISSING_DATA = 'Missing_Data';
    @TestVisible
    private static final String SOLUTION_DOES_NOT_EXIST = 'Solution_does_not_exist';
    private static final String OSB_CONTACT_ONEHUB_INVITE_EMAIL_TEMPLATE = 'OSB_API_Contact_OneHub_Invite';

    @HttpPost
    global static void completeContactOnboarding() {
        fflib_ISObjectUnitOfWork uow = ORG_Application.unitOfWork.newInstance();
        Contact onboardContact = new Contact();
        List<Knowledge__kav> articles = new List<Knowledge__kav>();
        RestResponse res = RestContext.response;
        res.addHeader('Content-Type', 'application/json');
        Contact_Onboarding_Response_Body contactResponseBody = new Contact_Onboarding_Response_Body();
        RestRequest req = RestContext.request;
        String requestBody = req.requestBody.toString();

        Contact_Onboarding_Request receivedRequest = (Contact_Onboarding_Request) JSON.deserialize(
            requestBody,
            Contact_Onboarding_Request.class
        );
        try {
            validateAllField(receivedRequest);

            List<Contact> existingContactList = SEL_Contacts.newInstance()
                .selectByEmail(new Set<String>{ receivedRequest.email });

            articles = SEL_KnowledgeArticleVersions.newInstance()
                .selectByPublishStatusAndTitleWoSharing(
                    new Set<String>{ DMN_Knowledge.PUBLISH_STATUS_ONLINE },
                    new Set<String>{ receivedRequest.appName }
                );

            if (articles.isEmpty()) {
                throw new OSBAPIContactOnboardingException(
                    SOLUTION_DOES_NOT_EXIST +
                    ': no solution found with the name: ' +
                    receivedRequest.appName,
                    SOLUTION_DOES_NOT_EXIST
                );
            }

            if (existingContactList.isEmpty()) {
                onboardContact = populateContactDetails(
                    receivedRequest,
                    onboardContact
                );

                List<Account> accountList = SEL_Accounts.newInstance()
                    .selectByCIFNumber(
                        new Set<String>{ receivedRequest.cifNumber }
                    );

                if (accountList.isEmpty()) {
                    throw new OSBAPIContactOnboardingException(
                        ACCOUNT_DOES_NOT_EXIST +
                        ': no account found with cif number: ' +
                        receivedRequest.cifNumber,
                        ACCOUNT_DOES_NOT_EXIST
                    );
                } else {
                    onboardContact.AccountId = accountList[0].Id;
                }

                onboardContact.Community_Solution_Invitation__c = receivedRequest.appName;

                onboardContact = populateContactBasedOnExistingPingId(
                    receivedRequest,
                    onboardContact
                );

                uow.registerNew(onboardContact);
            } else {
                onboardContact = existingContactList[0];

                if (
                    onboardContact.Inactive__c ||
                    (onboardContact.OSB_Community_Access_Status__c ==
                    DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_INACTIVE) ||
                    (onboardContact.OSB_Community_Access_Status__c ==
                    DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_DECLINED)
                ) {
                    onboardContact.OSB_Community_Access_Status__c = onboardContact.Inactive__c
                        ? DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_INACTIVE
                        : DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_DECLINED;
                }

                Boolean activeContactStatus = (onboardContact.OSB_Community_Access_Status__c ==
                    DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_INVITE_SENT) ||
                    (onboardContact.OSB_Community_Access_Status__c ==
                    DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_APPROVED)
                    ? true
                    : false;

                if (
                    (activeContactStatus ||
                    (String.isBlank(
                        onboardContact.OSB_Community_Access_Status__c
                    ))) && String.isBlank(onboardContact.Ping_Id__c)
                ) {
                    onboardContact = populateContactDetails(
                        receivedRequest,
                        onboardContact
                    );

                    onboardContact.Community_Solution_Invitation__c = receivedRequest.appName;

                    onboardContact = populateContactBasedOnExistingPingId(
                        receivedRequest,
                        onboardContact
                    );

                    uow.registerDirty(onboardContact);
                }
            }

            uow.commitWork();

            if (
                onboardContact.OSB_Community_Access_Status__c.contains(
                    DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_INVITE_SENT
                )
            ) {
                onboardContact = updateOnboardContactCommunityURL(
                    onboardContact
                );

                if (receivedRequest.sendEmail) {
                    Map<String, Object> inputs = new Map<String, Object>();
                    inputs.put('contactRecordID', onboardContact.Id);
                    inputs.put(
                        'emailTemplateName',
                        OSB_CONTACT_ONEHUB_INVITE_EMAIL_TEMPLATE
                    );

                    if (!Test.isRunningTest()) {
                        Flow.Interview.OSB_Contact_Onboarding_Email_Alerts myFlow = new Flow.Interview.OSB_Contact_Onboarding_Email_Alerts(
                            inputs
                        );
                        myFlow.start();
                    }
                }
            }

            contactResponseBody = generateResponseBody(
                onboardContact,
                receivedRequest
            );

            res.statusCode = 200;
            res.responseBody = Blob.valueOf(
                JSON.serializePretty(contactResponseBody)
            );

            List<Subscribed_Solutions__c> existingSubscribedSolutionsList = SEL_SubscribedSolutions.newInstance()
                .selectByContactIdAndSolutionId(
                    new Set<Id>{ articles[0].Id },
                    new Set<Id>{ onboardContact.Id }
                );

            if (existingSubscribedSolutionsList.isEmpty()) {
                createSubscribedSolution(articles, onboardContact);
            }
        } catch (OSBAPIContactOnboardingException e) {
            Error_Response_Body errorResponse = new Error_Response_Body();
            errorResponse.ErrorCode = e.exType;
            errorResponse.Message = e.exMessage;
            res.responseBody = Blob.valueOf(
                JSON.serializePretty(errorResponse)
            );
            res.statusCode = 400;

            LOGGER.error(
                'OSB_API_Contact_Onboarding : completeContactOnboarding Exception logged: ',
                e
            );
        } catch (Exception e) {
            LOGGER.error(
                'OSB_API_Contact_Onboarding : completeContactOnboarding Exception logged: ',
                e
            );

            throw e;
        }
    }

    private static Contact populateContactDetails(
        Contact_Onboarding_Request receivedRequest,
        Contact contactDetails
    ) {
        contactDetails.FirstName = receivedRequest.firstName;
        contactDetails.LastName = receivedRequest.lastName;
        contactDetails.Email = receivedRequest.email;
        contactDetails.Phone = receivedRequest.cellPhoneNumber;
        contactDetails.Title = receivedRequest.jobTitle;
        contactDetails.OSB_Operating_Country__c = receivedRequest.operatingCountry;
        contactDetails.Phone_Country__c = receivedRequest.operatingCountry;
        contactDetails.OSB_Company_name__c = receivedRequest.companyName;
        contactDetails.RecordTypeId = UTL_RecordType.getRecordTypeId(
            'Contact',
            DMN_Contact.RTD_CLIENT
        );
        contactDetails.OSB_Community_Access_Role__c = String.isNotEmpty(
                contactDetails.OSB_Community_Access_Role__c
            )
            ? contactDetails.OSB_Community_Access_Role__c
            : DMN_Contact.OSB_COMMUNITY_ACCESS_ROLE_NP;
        return contactDetails;
    }

    private static void validateAllField(
        Contact_Onboarding_Request receivedRequest
    ) {
        List<String> fieldFailedValidationList = new List<String>();
        String emailRegex = '^[a-zA-Z0-9._|\\\\%#~`=?&/$^*!}{+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$';
        Pattern emailPattern = Pattern.compile(emailRegex);
        Matcher emailMatch = emailPattern.matcher(receivedRequest.email);

        if (String.isBlank(receivedRequest.firstName)) {
            fieldFailedValidationList.add('firstName');
        }

        if (String.isBlank(receivedRequest.lastName)) {
            fieldFailedValidationList.add('lastName');
        }

        if (String.isBlank(receivedRequest.email)) {
            fieldFailedValidationList.add('email');
        }

        if (String.isBlank(receivedRequest.cellPhoneNumber)) {
            fieldFailedValidationList.add('cellPhoneNumber');
        }

        if (String.isBlank(receivedRequest.jobTitle)) {
            fieldFailedValidationList.add('jobTitle');
        }

        if (String.isBlank(receivedRequest.cifNumber)) {
            fieldFailedValidationList.add('cifNumber');
        }

        if (String.isBlank(receivedRequest.companyName)) {
            fieldFailedValidationList.add('companyName');
        }

        if (String.isBlank(receivedRequest.appName)) {
            fieldFailedValidationList.add('appName');
        }

        if (String.isBlank(receivedRequest.operatingCountry)) {
            fieldFailedValidationList.add('operatingCountry');
        }

        if (!fieldFailedValidationList.isEmpty()) {
            throw new OSBAPIContactOnboardingException(
                MISSING_DATA +
                ': the following fields contain missing data: ' +
                fieldFailedValidationList.toString(),
                MISSING_DATA
            );
        }

        if (!emailMatch.matches()) {
            throw new OSBAPIContactOnboardingException(
                MISSING_DATA + ': email is not a valid format.',
                MISSING_DATA
            );
        }
    }

    private static Contact populateContactBasedOnExistingPingId(
        Contact_Onboarding_Request receivedRequest,
        Contact onboardContact
    ) {
        String pingId = OSB_SRV_PingIntegration.newInstance()
            .getUser(receivedRequest.email);

        if (String.isBlank(pingId)) {
            if (receivedRequest.sendEmail) {
                onboardContact.OSB_Community_Access_Status__c = DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_INVITE_SENT_VIA_EMAIL;
            } else {
                onboardContact.OSB_Community_Access_Status__c = DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_INVITE_SENT_VIA_URL;
            }
        } else {
            onboardContact.Ping_Id__c = pingId;
            onboardContact.OSB_Community_Access_Status__c = DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_APPROVED;
        }

        return onboardContact;
    }

    @TestVisible
    private static void createSubscribedSolution(
        List<Knowledge__kav> articles,
        Contact onboardContact
    ) {
        fflib_ISObjectUnitOfWork uow = ORG_Application.unitOfWork.newInstance();
        Subscribed_Solutions__c subscribedSolution = new Subscribed_Solutions__c();
        subscribedSolution.Solution__c = articles[0].Id;
        subscribedSolution.Contact__c = onboardContact.Id;
        subscribedSolution.RecordTypeId = UTL_RecordType.getRecordTypeId(
            BLD_Subscribed_Solutions.OBJ_NAME,
            BLD_Subscribed_Solutions.RT_APPLICATION
        );
        uow.registerNew(subscribedSolution);
        uow.commitWork();
    }

    @TestVisible
    private static Contact updateOnboardContactCommunityURL(
        Contact onboardContact
    ) {
        fflib_ISObjectUnitOfWork uow = ORG_Application.unitOfWork.newInstance();
        if (String.isEmpty(onboardContact.Onehub_Community_URL__c)) {
            onboardContact.Onehub_Community_URL__c =
                OSB_URLs__c.getValues(OSB_CUSTOM_SETTING_OSB_BASE_URL)
                    .Value__c +
                's/sign-up?record=' +
                OSB_SRV_EncryptionHelper.encryptString(
                    onboardContact.Id,
                    OSB_MAIL_ENCRKEY
                );
        }
        uow.registerDirty(onboardContact);
        uow.commitWork();

        return onboardContact;
    }

    private static Contact_Onboarding_Response_Body generateResponseBody(
        Contact contactDetails,
        Contact_Onboarding_Request receivedRequest
    ) {
        Contact_Onboarding_Response_Body contactBody = new Contact_Onboarding_Response_Body();
        contactBody.firstName = contactDetails.FirstName;
        contactBody.lastName = contactDetails.LastName;
        contactBody.email = contactDetails.Email;
        contactBody.cellPhoneNumber = contactDetails.Phone;
        contactBody.jobTitle = contactDetails.Title;
        contactBody.companyName = contactDetails.OSB_Company_name__c;
        contactBody.pingId = contactDetails.Ping_Id__c;
        contactBody.status = contactDetails.OSB_Community_Access_Status__c;
        contactBody.solutionInvite = contactDetails.Community_Solution_Invitation__c;
        contactBody.registrationURL = contactDetails.Onehub_Community_URL__c;
        contactBody.cifNumber = receivedRequest.cifNumber;

        return contactBody;
    }

    @TestVisible
    private class Contact_Onboarding_Request {
        public String firstName;
        public String lastName;
        public String email;
        public String cellPhoneNumber;
        public String jobTitle;
        public String cifNumber;
        public String companyName;
        public String appName;
        public Boolean sendEmail;
        public String operatingCountry;
    }

    global class Contact_Onboarding_Response_Body {
        public String firstName;
        public String lastName;
        public String email;
        public String cellPhoneNumber;
        public String jobTitle;
        public String cifNumber;
        public String companyName;
        public String status;
        public String pingId;
        public String registrationURL;
        public String solutionInvite;
    }

    global class Error_Response_Body {
        private String ErrorCode { get; set; }
        private String Message { get; set; }
    }

    public class OSBAPIContactOnboardingException extends Exception {
        private final String exType { get; set; }
        private final String exMessage { get; set; }
        /**
         * The Exception Method
         * @param message error dessciption
         * @param exceptionType the type of the exception
         */
        public OSBAPIContactOnboardingException(
            String message,
            String exceptionType
        ) {
            exMessage = message;
            exType = exceptionType;
        }
    }
}
