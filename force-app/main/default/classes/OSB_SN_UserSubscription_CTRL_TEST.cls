/**
 * @description Test class for OSB_SN_UserSubscription_CTRL
 *
 * <AUTHOR> (<EMAIL>)
 * @UserStory SFP-25116
 * @date May 2023
 */

@IsTest
private class OSB_SN_UserSubscription_CTRL_TEST {
    public static final String SOL_TITLE = 'Test article';
    public static final String SUBSCRIBED = 'Yes';
    public static final String EMAIL_ALERTS = 'Yes';
    public static final String EMAIL_FREQ = 'Weekly';

    @IsTest
    static void shouldGetUserPreferences() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();

        SEL_SubscribedSolutions preferenceSel = (SEL_SubscribedSolutions) mocks.mock(
            SEL_SubscribedSolutions.class
        );

        Subscribed_Solutions__c preference = (Subscribed_Solutions__c) new BLD_Subscribed_Solutions()
            .mock();

        mocks.startStubbing();
        mocks.when(preferenceSel.SObjectType())
            .thenReturn(Subscribed_Solutions__c.SObjectType);
        mocks.when(
                preferenceSel.selectByUserEmailAndSolutionTitles(
                    (Set<String>) fflib_Match.anyObject(),
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Subscribed_Solutions__c>{ preference });
        mocks.stopStubbing();

        ORG_Application.selector.setMock(preferenceSel);

        Test.startTest();
        Subscribed_Solutions__c preferences = OSB_SN_UserSubscription_CTRL.getUserPreferences(
            SOL_TITLE
        );
        Test.stopTest();

        System.assertEquals(
            preference,
            preferences,
            'Did not get the correct Solution'
        );
    }
    @IsTest
    static void shouldGetUserSubscription() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();

        SEL_SubscribedSolutions preferenceSel = (SEL_SubscribedSolutions) mocks.mock(
                SEL_SubscribedSolutions.class
        );

        Subscribed_Solutions__c preference = (Subscribed_Solutions__c) new BLD_Subscribed_Solutions()
                .setSubscribed(SUBSCRIBED)
                .mock();

        mocks.startStubbing();
        mocks.when(preferenceSel.SObjectType())
                .thenReturn(Subscribed_Solutions__c.SObjectType);
        mocks.when(
                preferenceSel.selectByUserEmailAndSolutionTitles(
                        (Set<String>) fflib_Match.anyObject(),
                        (Set<String>) fflib_Match.anyObject()
                )
        )
                .thenReturn(new List<Subscribed_Solutions__c>{ preference });
        mocks.stopStubbing();

        ORG_Application.selector.setMock(preferenceSel);

        Test.startTest();
        Boolean preferences = OSB_SN_UserSubscription_CTRL.getUserSubscription(
                SOL_TITLE
        );
        Test.stopTest();

        System.assertEquals(
                true,
                preferences,
                'Did not get the user subscription'
        );
    }

    @IsTest
    static void shouldSaveUserSubscription() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        fflib_ISObjectUnitOfWork uowMock = new fflib_SObjectMocks.SObjectUnitOfWork(
            mocks
        );

        SEL_SubscribedSolutions preferenceSel = (SEL_SubscribedSolutions) mocks.mock(
            SEL_SubscribedSolutions.class
        );

        Subscribed_Solutions__c preference = (Subscribed_Solutions__c) new BLD_Subscribed_Solutions()
            .setSubscribed(SUBSCRIBED)
            .setEmailAlerts(EMAIL_ALERTS)
            .setEmailFrequency(EMAIL_FREQ)
            .mock();

        mocks.startStubbing();
        mocks.when(preferenceSel.SObjectType())
            .thenReturn(Subscribed_Solutions__c.SObjectType);
        mocks.when(
                preferenceSel.selectByUserEmailAndSolutionTitles(
                    (Set<String>) fflib_Match.anyObject(),
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Subscribed_Solutions__c>{ preference });
        mocks.stopStubbing();

        ORG_Application.selector.setMock(preferenceSel);
        ORG_Application.unitOfWork.setMock(uowMock);

        Test.startTest();
        String response = OSB_SN_UserSubscription_CTRL.saveUserPreferences(
            SUBSCRIBED,
            EMAIL_ALERTS,
            EMAIL_FREQ,
            SOL_TITLE
        );
        Test.stopTest();

        System.assertEquals(
            'Success',
            response,
            'Did not update the user subscription preferences'
        );
        System.assertNotEquals(null, response, 'Could not find subscription');
        ((fflib_ISObjectUnitOfWork) mocks.verify(uowMock, 1)).commitWork();
    }
}