/**
 * @description Selector class for AuthorizationFormText SObject
 *
 * <AUTHOR> (<EMAIL>)
 * @date September 2024
 * @UserStory SFP-36886
 */
public without sharing class SEL_AuthorizationFormText extends fflib_SObjectSelector{

    /**
     * @description Creates a new instance of the selector via the application class. This is here to allow unit tests to override
	 * and inject a mock instead of this class or to switch out this class for a new version.
     * 
     * @return SEL_AuthorizationFormText class instance
     */
    public static SEL_AuthorizationFormText newInstance() {
        return (SEL_AuthorizationFormText) ORG_Application.selector
            .newInstance(AuthorizationFormText.SObjectType);
    }

    /**
     * @description Returns the SObject type for the selector. This is used to retrieve the sObject name when building the SOQL
     * queries.
     * 
     * @return AuthorizationFormText object type
     */
    public Schema.SObjectType getSObjectType() {
        return AuthorizationFormText.SObjectType;
    }

    /**
    * @description Returns the SObject fields on this sObject
    * 
    * @return SEL_AuthorizationFormText fields list
    */
    public List<SObjectField> getSObjectFieldList() {
        return new List<Schema.SObjectField>{
            AuthorizationFormText.Id,
            AuthorizationFormText.AuthorizationFormId,
            AuthorizationFormText.DetailAuthorizationFormText,
            AuthorizationFormText.IsActive,
            AuthorizationFormText.Locale,
            AuthorizationFormText.Name,
            AuthorizationFormText.TextVersion__c
        };
    }

    /**
    * @description Returns list of authorization form texts by id
    * @param idSet Set<Id> set of authorization form text ids
	*
    * @return list of selected Authorization Form Texts
    */
    public List<AuthorizationFormText> selectById(Set<Id> idSet) {
        return (List<AuthorizationFormText>) Database.query(
            newQueryFactory()
            .setCondition('Id IN :idSet')
            .toSOQL()
        );
    } 
}