/**
 * @description OSB_OtpManagement_CTRL class for osbOtpModal Lightning Component
 * <AUTHOR> (<EMAIL>)
 * @date May 2024
 * UserStory SFP-38763
 *
 * @LastModified February 2025
 * <AUTHOR> (<EMAIL>)
 * @UserStory
 * @LastModifiedReason Modification to sendOutMailOTP method, to pass a Contact or OTP Request ID
 *
 * @LastModified February 2025
 * <AUTHOR> (<EMAIL>)
 * @UserStory SFP-46065
 * @LastModifiedReason added retrieveOTPEnablementFirstDevice method to retrieve the OTP Enablement First Device
 **/

public with sharing class OSB_OtpManagement_CTRL {
    public static final String DEVELOPERNAME = 'OneHub';
    public static final String FEATURELABEL = 'OTP_Enablement';
    public static final String OTPFEATURELABEL = 'OTP_Enablement_First_Device';
    private static final sbgplatform.rflib_Logger LOGGER = sbgplatform.rflib_LoggerUtil.getFactory().createLogger('OSB_OtpManagement_CTRL');

    public class OTPManagementException extends Exception {}

    /**
     * @description  Retrieving the Onehub Custom metadata record from the OTP Request Setting.
     * @return String
     **/
    @AuraEnabled
    public static OTP_Request_Setting__mdt getCustomMetadataRecord() {
     OTP_Request_Setting__mdt metadataRecord;
        try {
            metadataRecord = OTP_Request_Setting__mdt.getInstance(DEVELOPERNAME);
        } catch (Exception e) {
            LOGGER.error(
                'OSB_OtpManagement_CTRL : getCustomMetadataRecord Exception logged: ',
                e
            );
        }
        return metadataRecord;
    }

    /**
     * @description Requests for an OTP code for the respective user
     * 
     * @param otpReason String
     * @return SRV_OTP.Response wrapper class
     **/
    @AuraEnabled
    public static SRV_OTP.Response initialiseOTP(String otpReason) {
        Id userId = UserInfo.getUserID();
        SRV_OTP.Response  otpInitialise;
        try {
            Contact userContact = SEL_Contacts.newInstance().selectByUserId(new Set<Id>{ userId })[0];
            List<OTPRequest__c> existingRequests = SEL_OTPRequest.newInstance().selectByRelatedRecordId(userContact.Id);
            SRV_OTP service = (SRV_OTP) SRV_OTP.newInstance();
            service.initialise(DEVELOPERNAME, userContact.Id);
            if (!existingRequests.isEmpty()) {
                fflib_SObjectUnitOfWork uow = new fflib_SObjectUnitOfWork(
                    new List<SObjectType>{ OTPRequest__c.SObjectType }
                );
                OTPRequest__c existingRequest = existingRequests[0];
                existingRequest.CodeExpiresAt__c = System.now().addHours(-1);
                uow.registerDirty(existingRequest);
                uow.commitWork();
            }
            otpInitialise = service.requestCode();
        } catch (Exception e) {
            LOGGER.error(
                'OSB_OtpManagement_CTRL : initialiseOTP Exception logged: ',
                e
            );
            throw new AuraHandledException(e.getMessage()); 
        }
        return otpInitialise;
    }

    /**
     * @description Validates an OTP code provided by the user.
     * 
     * @param  String inputCode
     * @return Boolean response
     **/
    @AuraEnabled
    public static Boolean validateOTPCode(String inputCode) {
        Boolean result;
        SRV_OTP.Response otpValidation;
        try {
            Id userId = UserInfo.getUserID();
            Contact userContact = SEL_Contacts.newInstance().selectByUserId(new Set<Id>{ userId })[0];
            SRV_OTP service = (SRV_OTP) SRV_OTP.newInstance();
            service.initialise(DEVELOPERNAME, userContact.Id);
            otpValidation = service.validateCode(inputCode);
        } catch (Exception e) {
            LOGGER.error(
                'OSB_OtpManagement_CTRL : Validate OTP Exception logged: ',
                e
            );
            throw new AuraHandledException(e.getMessage()); 
        }

        if(otpValidation.isSuccess){
            return result;
        }else{
            throw new OTPManagementException('Validation Failure');
        }
    }
    /**
     * @description Creates a case and assign it to the OneHub queue.
     * SFP-38763
     * @return created Case record
     **/
    @AuraEnabled
    public static Case createCase() {
        Case caseRecord = new Case();
        Case createdCase = new Case();
        try {
            Id userId = UserInfo.getUserID();
            Contact userContact = SEL_Contacts.newInstance().selectByUserId(new Set<Id>{ userId })[0];
            caseRecord.Subject = 'Suspicious activity detected';
            caseRecord.Description =
                'We have detected some unusual activity for ' +
                userContact.FirstName +
                ' ' +
                userContact.LastName +
                ' six or more OTP attempts. Please get in touch with them so we can help ensure the security of their OneHub account.';
            caseRecord.OwnerId = UTL_Queue.getQueueId(DMN_Queue.ONEHUB_QUEUE);
            createdCase = DMN_Case.createCaseWithUserContactId(caseRecord); 
            
        } catch (Exception e) {
            LOGGER.error(
                'OSB_OtpManagement_CTRL : Create case Exception logged: ',
                e
            );
        }
        return createdCase;
    }

    /**
     * @description A method to invoke OTP emails to a flow.
     *
     * @param  otpId String
     * @param  otpReason String
     **/
    @AuraEnabled
    public static void sendOutMailOTP(String otpReason) {
        try {
            Contact userContact = SEL_Contacts.newInstance().selectByUserId(new Set<Id>{ UserInfo.getUserID() })[0];
            String contactId = userContact.Id;
 
            String recordId;
            if (otpReason.contains('Successful')) {
                recordId = userContact.Id;
            } else {
                OTPRequest__c otpRequest = SEL_OTPRequest.newInstance().selectByRelatedRecordId(userContact.Id)[0];
                recordId = otpRequest.Id;
            }
 
            Map<String, Object> inputs = new Map<String, Object>();
            inputs.put('otpAction', otpReason);
            inputs.put('recordID', recordId);
            inputs.put('contactID', contactId);
 
            if (!Test.isRunningTest()) {
                Flow.Interview.OSB_OTP_Email_Alerts myFlow = new Flow.Interview.OSB_OTP_Email_Alerts(inputs);
                myFlow.start();
            }
   
        } catch (Exception e) {
            LOGGER.error(
                'OSB_OtpManagement_CTRL : send Out Mail OTP Exception logged: ',
                e
            );
            throw new AuraHandledException(e.getMessage());
        }
    }
    
    /**
     * @description  Retrieving the OTP Enablement Custom metadata record from the OSB Feature Management setting.
     * @return String
     **/
    @AuraEnabled(cacheable=true)
    public static OSB_Feature_Management__mdt retrieveFeatureManagementRecord() {
        OSB_Feature_Management__mdt metadataRecord;
        try {
             metadataRecord = OSB_Feature_Management__mdt.getInstance(FEATURELABEL);
            
        } catch (Exception e) {
            LOGGER.error(
                'OSB_OtpManagement_CTRL : retrieve Feature Management Record Exception logged: ',
                e
            );
        }
        return metadataRecord;
    }
    /**
     * @description  Retrieving the OTP Enablement First Device Addition Custom metadata record from the OSB Feature Management setting.
     * @return String
     **/
    @AuraEnabled(cacheable=true)
    public static OSB_Feature_Management__mdt retrieveOTPEnablementFirstDevice() {
        OSB_Feature_Management__mdt metadataRecord;
        try {
             metadataRecord = OSB_Feature_Management__mdt.getInstance(OTPFEATURELABEL);
            
        } catch (Exception e) {
            LOGGER.error(
                'OSB_OtpManagement_CTRL : retrieve Feature Management Record Exception logged: ',
                e
            );
        }
        return metadataRecord;
    }
   
}