/********************************************
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date    	: 15 August 2022
 * @description : Batch class to Send email notifications to CST members
 * *****************************************/
global class SmartNudge_BatchInsightsInMyPortfolio implements Database.Batchable<sObject>, Schedulable {
    public Set<Id> userIds;

    /******
     * @description : Preparing list of CST members from the last 7 days insights.
     * *****/
    global SmartNudge_BatchInsightsInMyPortfolio() {
        userIds = new Set<Id>();
        List<Insight__c> insightList = new SEL_Insights()
            .getInsightsWithCondition(
                'Event_Date__c = LAST_N_DAYS:7 AND Opportunity__c = null AND Is_Expired__c=false AND Is_Snoozed__c = false AND Is_Provided_Feedback__c=false',
                40000
            );

        if (!insightList.isEmpty()) {
            Set<id> insightIds = new Set<Id>();
            for (Insight__c insight : insightList) {
                userIds.add(insight.OwnerId);
                if (insight.Client_Analyst__c != null) {
                    userIds.add(insight.Client_Analyst__c);
                }
                if (insight.Client_Coordinator__c != null) {
                    userIds.add(insight.Client_Coordinator__c);
                }
                insightIds.add(insight.Id);
            }
            for (Insight__Share insightShare : [
                SELECT Id, UserOrGroupId
                FROM Insight__Share
                WHERE ParentId IN :insightIds
            ]) {
                userIds.add(insightShare.UserOrGroupId);
            }
        }
    }
    /******
     * @description : start method to fetch CST members
     * @return user records
     * @param bc
     * *****/
    global Database.QueryLocator start(Database.BatchableContext bc) {
        return Database.getQueryLocator(
            [
                SELECT id, Name, Email
                FROM User
                WHERE Id IN :userIds AND isActive = TRUE
            ]
        );
    }
    /******
     * @description execute method to send email notifications to CST members
     * @param bc
     * @param userList
     * *****/
    global void execute(Database.BatchableContext bc, List<User> userList) {
        List<EmailTemplate> templateList = new SEL_EmailTemplate()
            .selectByName('SmartNudge_NotificationsToCSTmembers');
        List<Messaging.SingleEmailMessage> messages = new List<Messaging.SingleEmailMessage>();
        List<OrgWideEmailAddress> oweaList = new SEL_OrgWideEmailAddress()
            .selectByAddress('<EMAIL>');

        if (!userList.isEmpty()) {
            for (User userRecord : userList) {
                Messaging.SingleEmailMessage message = Messaging.renderStoredEmailTemplate(
                    templateList[0].Id,
                    null,
                    userRecord.Id
                );
                message.setTemplateID(templateList[0].Id);
                message.setWhatId(userInfo.getUserId());
                message.setTreatTargetObjectAsRecipient(false);
                message.setTargetObjectId(userRecord.Id);
                message.setSaveAsActivity(false);
                if (!oweaList.isEmpty()) {
                    message.setOrgWideEmailAddressId(oweaList[0].Id);
                }
                message.setToAddresses(new List<String>{ userRecord.Email });
                messages.add(message);
            }
            Messaging.sendEmail(messages);
        }
    }
    /******
     * @description schedule method to call batch class with batchsize 90
     * @param sc
     * *****/
    global void execute(SchedulableContext sc) {
        SmartNudge_BatchInsightsInMyPortfolio clientsInMyPortfolio = new SmartNudge_BatchInsightsInMyPortfolio();
        Database.executeBatch(clientsInMyPortfolio, 90);
    }
    /******
     * @description finish method
     * @param bc
     * *****/
    global void finish(Database.BatchableContext bc) {
    }
}
