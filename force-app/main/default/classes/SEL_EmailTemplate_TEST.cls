/**
 * @description       : US: SFP-11298 Add to calendar from email functionality
 * <AUTHOR> TCK
 * @group             :
 * @last modified on  : 06-13-2022
 * @last modified by  : TCK
 **/
@IsTest
private class SEL_EmailTemplate_TEST {
    @IsTest
    static void shouldSelectByName() {
        String name;
        Test.startTest();
        SEL_EmailTemplate.newInstance().selectByName(name);
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        System.assert(
            result.getCondition().containsIgnoreCase('Name =: name'),
            'Query contains Name =: name'
        );
    }
}
