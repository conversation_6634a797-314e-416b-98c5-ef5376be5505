/**
 * @description       : SFP-10915 - Automatic App Attendee creation
 * <AUTHOR> TCK
 * @group             : 
 * @last modified on  : 07-21-2022
 * @last modified by  : TCK
**/
public with sharing class TRH_CampaignMember extends ABS_TriggerHandlerBase {
    private Map<Id, CampaignMember> id2OldRecords{
        get{
            if(Trigger.old == null){ return null; }
            return new Map<Id, CampaignMember>((CampaignMember[])Trigger.old);
        }
    }

    private Map<Id, CampaignMember> id2NewRecords {
        get {
            if (Trigger.newMap == null) { return null; }
            return (Map<Id, CampaignMember>) Trigger.newMap;
        }
    }

    // public override void handleAfterInsert(){
    //     // DMN_CampaignMember.createAttendeeFromInsert(id2NewRecords);
    // }

    public override void handleAfterUpdate(){
        DMN_CampaignMember.createAttendeeFromUpdate(id2NewRecords, id2OldRecords);
    }
}