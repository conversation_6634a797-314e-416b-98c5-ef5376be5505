/**
 * @description test class for SEL_Insights class
 * <AUTHOR>
 * @UserStory SFP-25120
 * @date DEC 2023
 */
@IsTest(IsParallel=true)
public class SEL_InsightActions_TEST {

    /**
    * @description shouldSelectByInsightIdsAndActionTypes
    **/
    @IsTest
    static void shouldSelectByInsightIdsAndActionTypes() {
        Test.startTest();
        new SEL_InsightActions().selectByInsightIdsAndActionTypes(new Set<String>(),new List<String>{'Is Snoozed'});
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Insight__c IN : insightIds AND Action_Type__c IN : actionTypes'), 'Condition used id set.');
    }
    /**
    * @description shouldSelectWithoutCondition
    **/
    @IsTest
    static void shouldSelectWithoutCondition() {
        Test.startTest();
        new SEL_InsightActions().selectWithoutCondition();
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(null, result.getCondition(), 'No condition used.');
    }
}