/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @description : Test class for SmartNudge_BatchInsightsInMyPortfolio
 * @date 		: 15th August 2022
 */
@isTest
public class SmartNudge_BatchInsights_TEST {
	/**
     * @description :  This method will test for more than 10 insights scenario
     */
    @isTest
    static void myLatestInsightsTest(){
        Test.startTest();
        List<Schema.SObjectType> MY_SOBJECTS = new Schema.SObjectType[]{Insight__c.SObjectType};
        fflib_SObjectUnitOfWork uow = new fflib_SObjectUnitOfWork(MY_SOBJECTS);

		BLD_Insight bAccount = new BLD_Insight(uow).category('FX TIPS');
		bAccount.owner(UserInfo.getUserId());
        bAccount.snoozed(false);
        bAccount.eventDate(Date.today());
        bAccount.insight('Test Insight');
        bAccount.clientCoordinator(UserInfo.getUserI<PERSON>());
        uow.commitWork();
        
        SmartNudge_BatchInsightsInMyPortfolio sch = new SmartNudge_BatchInsightsInMyPortfolio();
		String chroneExpression = '0 0 23 * * ?'; 
        System.schedule('My latest insights', chroneExpression, sch);
        Test.stopTest();
        System.assert(bAccount != null, 'Was expecting to find at least one insight');
    }
}