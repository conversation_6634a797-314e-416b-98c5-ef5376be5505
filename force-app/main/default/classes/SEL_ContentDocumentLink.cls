/**
 * @description       : Selector class for ContentDocumentLink SObject
 * <AUTHOR> TCK
 * @group             : 
 * @last modified on  : 06-14-2022
 * @last modified by  : TCK
**/
public without sharing class SEL_ContentDocumentLink extends fflib_SObjectSelector {
    
    /**
    * @description 
    * <AUTHOR> | 06-13-2022 
    * @return List<Schema.SObjectField> 
    **/
    public List<Schema.SObjectField> getSObjectFieldList() {

        return new List<Schema.SObjectField> {
            ContentDocumentLink.Id,
            ContentDocumentLink.LinkedEntityId,
            ContentDocumentLink.ContentDocumentId,
            ContentDocumentLink.Visibility
        };
    }

    /**
    * @description 
    * <AUTHOR> | 06-13-2022 
    * @return Schema.SObjectType 
    **/
    public Schema.SObjectType getSObjectType() {
        return ContentDocumentLink.sObjectType;
    }

    /**
    * @description 
    * <AUTHOR> | 06-13-2022 
    * @return SEL_ContentDocumentLink 
    **/
    public static SEL_ContentDocumentLink newInstance() {
        return(SEL_ContentDocumentLink) ORG_Application.selector.newInstance(ContentDocumentLink.SObjectType);
    }

    /**
    * @description 
    * <AUTHOR> | 06-13-2022 
    * @param linkedEntityId 
    * @return List<ContentDocumentLink> 
    **/
    public List<ContentDocumentLink> selectByLinkedEntityId(Set<Id> linkedEntityId) {
        return (List<ContentDocumentLink>) Database.query(
                        newQueryFactory()
                        .selectField('ContentDocument.LatestPublishedVersionId')
                        .setCondition('LinkedEntityId IN: linkedEntityId')
                        .toSOQL());
    }
}