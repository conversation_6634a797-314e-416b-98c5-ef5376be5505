/**
 * @description       : Selector class for ContentVersion SObject
 * <AUTHOR> TCK
 * @group             : 
 * @last modified on  : 06-14-2022
 * @last modified by  : TCK
**/
public without sharing class SEL_ContentVersion extends fflib_SObjectSelector {
    /**
    * @description 
    * <AUTHOR> | 06-13-2022 
    * @return List<Schema.SObjectField> 
    **/
    public List<Schema.SObjectField> getSObjectFieldList() {
        return new List<Schema.SObjectField> {
            ContentVersion.Id,
            ContentVersion.FileType,
            ContentVersion.ContentDocumentId,
            ContentVersion.ContentBodyId
        };
    }

    /**
    * @description 
    * <AUTHOR> | 06-13-2022 
    * @return Schema.SObjectType 
    **/
    public Schema.SObjectType getSObjectType() {
        return ContentVersion.sObjectType;
    }

    /**
    * @description 
    * <AUTHOR> | 06-13-2022 
    * @return SEL_ContentVersion 
    **/
    public static SEL_ContentVersion newInstance() {
        return(SEL_ContentVersion) ORG_Application.selector.newInstance(ContentVersion.SObjectType);
    }

    /**
    * @description 
    * <AUTHOR> | 06-13-2022 
    * @param contentDocumentIds 
    * @return List<ContentVersion> 
    **/
    public List<ContentVersion> selectByContentDocumentId(Set<Id> contentDocumentIds) {
        return (List<ContentVersion>) Database.query(
                        newQueryFactory()
                        .selectField('ContentDocument.LatestPublishedVersionId')
                        .setCondition('ContentDocumentId IN: contentDocumentIds')
                        .toSOQL());
    }
}