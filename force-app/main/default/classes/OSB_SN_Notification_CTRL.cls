/**
* @description Controller class for osbSmartNudgeNotifications component
* <AUTHOR>
* @UserStory SFP-25120
* @date Nov 2023
*/
public without sharing class OSB_SN_Notification_CTRL {
    private static final sbgplatform.rflib_Logger LOGGER = sbgplatform.rflib_LoggerUtil.getFactory().createLogger('OSB_SN_Notification_CTRL');
    
    /**
* @description To fetch the knowledge article record by urlname
* @param urlName
* @return Knowledge__kav 
**/
    @AuraEnabled
    public static Knowledge__kav getKnowledgeArticleByUrlName(String urlName){
        LOGGER.info('OSB_SN_Notification_CTRL:getKnowledgeArticleByUrlName initiated');
        Knowledge__kav knowledgeArticleRecord = new Knowledge__kav();
        try{    
            if(String.isNotBlank(urlName)){
                List<Knowledge__kav> knowledgeArticleList = new SEL_KnowledgeArticleVersions().selectByUrlNameWoSharing(new Set<String>{urlName});
                LOGGER.debug('OSB_SN_Notification_CTRL:knowledgeArticleList knowledge articles size :'+knowledgeArticleList.size());
                if(!knowledgeArticleList.isEmpty()){
                    knowledgeArticleRecord = knowledgeArticleList[0];
                }
            }
        }catch(Exception exp){
            throw new AuraHandledException(exp.getMessage());
        }
        return knowledgeArticleRecord;
    }
    /**
* @description Send notification to dealer with nudge details
* @param nudgeId
**/
    @AuraEnabled
    public static void sendNotificationToDealer(String nudgeId){
        LOGGER.info('OSB_SN_Notification_CTRL:sendNotificationToDealer initiated');
        try{            
            List<Insight__c> insightList = new SEL_Insights().selectInsightsByIdsWoSharing(new Set<String>{nudgeId});
            List<Messaging.SingleEmailMessage> messages = new List<Messaging.SingleEmailMessage>();
            Id oweaId = UTL_OrgWideEmailAddress.getAddressId('<EMAIL>');
            LOGGER.debug('OSB_SN_Notification_CTRL:sendNotificationToDealer oweaId :'+oweaId);
            EmailTemplate emailTemplate = UTL_EmailTemplate.getTemplate('OSB_SN_Contact_Dealer_Notification');
            LOGGER.debug('OSB_SN_Notification_CTRL:sendNotificationToDealer emailTemplate :'+emailTemplate);
            Messaging.SingleEmailMessage message = Messaging.renderStoredEmailTemplate(emailTemplate.Id, userInfo.getUserId(), nudgeId);
            message.setTemplateID(emailTemplate.Id);
            message.setTreatTargetObjectAsRecipient(false);
            message.setTargetObjectId(userInfo.getUserId());
            message.setSaveAsActivity(false);
            message.setOrgWideEmailAddressId(oweaId);         
            message.setToAddresses(new String[] {insightList[0].Owner.Email});
            system.debug('email address>>' +insightList[0].Owner.Email);
            messages.add(message);
            List<Messaging.SendEmailResult> results = 
                Messaging.sendEmail(messages);		
            if (!results.get(0).isSuccess()) {
                System.StatusCode statusCode = results.get(0).getErrors()[0].getStatusCode();
                String errorMessage = results.get(0).getErrors()[0].getMessage();
                system.debug('errorMessage> ' +errorMessage);
            } else {
                system.debug('results>>' +results);
            }
            //Messaging.sendEmail(messages); 
            LOGGER.debug('OSB_SN_Notification_CTRL:sendNotificationToDealer notification sent to dealer successfully');
            
            Insight__c insight = new Insight__c();
            insight.Id = nudgeId;
            insight.Is_Client_Interacted_Via_OneHub__c = true;
            Database.update(insight);
            LOGGER.debug('OSB_SN_Notification_CTRL:sendNotificationToDealer insight updated successfully');
            createInsightActionAndSendFeedbackToSmartNudge(nudgeId,'Contact Dealer',insightList[0].External_Lead_ID__c);
            LOGGER.debug('OSB_SN_Notification_CTRL:sendNotificationToDealer insight action created and send feedback to smartnudge with action details successfully');
        }catch(Exception exp){
            LOGGER.error('OSB_SN_Notification_CTRL : sendNotificationToDealer Exception logged: ',exp);
            throw new AuraHandledException(exp.getMessage());
        }
    }
    
    /**
    * @description To get nudge details PDF as base64 format
    * @param nudgeId
    * @return String 
    **/
    @AuraEnabled
    public static String getPdfFileAsBase64String(String nudgeId){
        LOGGER.info('OSB_SN_Notification_CTRL:getPdfFileAsBase64String initiated');
        String base64Pdf = '';
        try{            
            PageReference pdfPage = Page.OSB_SN_DownloadNudge;
            pdfPage.getParameters().put('id',nudgeId);
            Blob pdfBlob;
            if(!Test.isRunningTest()){
                pdfBlob = pdfPage.getContent();
            }else{
                pdfBlob = blob.valueof('TEST');
            }
            base64Pdf = EncodingUtil.base64Encode(pdfBlob);
        }
        catch(Exception exp){
            LOGGER.error('OSB_SN_Notification_CTRL : getPdfFileAsBase64String Exception logged: ',exp);
            throw new AuraHandledException(exp.getMessage());
        }
        return base64Pdf;
    }
    /**
    * @description It will fetch and return insights
    * @return Result 
    **/
    @AuraEnabled
    public static Result getUnreadInsights(){
        LOGGER.info('OSB_SN_Notification_CTRL:getUnreadInsights initiated');
        Result result =  new Result();
        try{
            result.insightList = new List<Insight__c>();
            Set<String> insightIds = retrieveUnreadInsightIds();
            LOGGER.debug('OSB_SN_Notification_CTRL:getUnreadInsights unread insights size :'+insightIds.size());
            if(!insightIds.isEmpty()){
                result.insightList = new SEL_Insights().selectInsightsByIdsWoSharing(insightIds);
                result.categories = getCategoryFilterValues(result.insightList);
            } 
        }catch(Exception exp){
            throw new AuraHandledException(exp.getMessage());
        }
        return result;
    }
    /**
    * @description It will fetch and return unread insight ids
    * @return Set<String> 
    **/
    public static Set<String> retrieveUnreadInsightIds(){
        LOGGER.info('OSB_SN_Notification_CTRL:retrieveUnreadInsightIds initiated');
        Set<String> insightIds = new Set<String>();
        try{
            List<User> userList = new SEL_Users().selectById(new Set<Id>{UserInfo.getUserId()});
            List<Insight_Client_Relationship__c> insightClientRelationshipList = new SEL_InsightClientRelationships().selectByContactIdAndIsUnreadWoSharing(new Set<String>{userList[0].ContactId},true);
            LOGGER.debug('OSB_SN_Notification_CTRL:retrieveUnreadInsightIds insight client relationship size :'+insightClientRelationshipList.size());
            if(!insightClientRelationshipList.isEmpty()){
                for(Insight_Client_Relationship__c insightClientRelationShip : insightClientRelationshipList ){
                    insightIds.add(insightClientRelationShip.Insight__c);
                }  
            }
        }catch(Exception exp){
            throw new AuraHandledException(exp.getMessage());
        }
        return insightIds;
    }
    
    /**
    * @description It will fetch and returns saved insights
    * @return Result 
    **/
    @AuraEnabled
    public static Result getSavedInsights(){
        LOGGER.info('OSB_SN_Notification_CTRL:getSavedInsights initiated');
        Result result =  new Result();
        try{
            result.insightList = new List<Insight__c>();
            List<User> userList = new SEL_Users().selectById(new Set<Id>{UserInfo.getUserId()});
            Set<String> insightIds = new Set<String>();
            List<Insight_Client_Relationship__c> insightClientRelationshipList = new SEL_InsightClientRelationships().selectByContactIdAndIsSnoozedWoSharing(new Set<String>{userList[0].ContactId},true);
            LOGGER.debug('OSB_SN_Notification_CTRL:getSavedInsights insight client relationship size :'+insightClientRelationshipList.size());
            if(!insightClientRelationshipList.isEmpty()){
                for(Insight_Client_Relationship__c insightClientRelationShip : insightClientRelationshipList ){
                    insightIds.add(insightClientRelationShip.Insight__c);
                } 
                result.insightList = new SEL_Insights().selectInsightsByIdsWoSharing(insightIds);
                result.categories = getCategoryFilterValues(result.insightList);
            }
        }catch(Exception exp){
            throw new AuraHandledException(exp.getMessage());
        }
        return result;
    }
    
    /**
    * @description Returns a list of insights
    * @return Result 
    **/
    @AuraEnabled
    public static Result getInsightsData(){
        LOGGER.info('OSB_SN_Notification_CTRL:getInsightsData initiated');
        Result result =  new Result();
        try{
            Set<String> isnightIds = new Set<String>();
            List<User> userList = new SEL_Users().selectById(new Set<Id>{UserInfo.getUserId()});
            LOGGER.debug('OSB_SN_Notification_CTRL:getInsightsData user record :'+userList);
            List<String> unreadInsightIds = new List<String>();
            List<Insight_Client_Relationship__c> insightClientRelationshipList = new SEL_InsightClientRelationships().selectByContactIdWoSharing(new Set<String>{userList[0].ContactId});
            LOGGER.debug('OSB_SN_Notification_CTRL:getInsightsData insight client relationship size :'+insightClientRelationshipList.size());
            
            for(Insight_Client_Relationship__c insightClientRelationShip : insightClientRelationshipList){
                isnightIds.add(insightClientRelationShip.Insight__c);
                if(insightClientRelationShip.Is_Unread__c){
                    unreadInsightIds.add(insightClientRelationShip.Insight__c);
                }
            }
            
            result.insightList = new List<Insight__c>();
            result.insightList = new SEL_Insights().selectInsightsByIdsWoSharing(isnightIds);
            result.unreadInsightIds = unreadInsightIds;
            result.categories = getCategoryFilterValues(result.insightList);
        }catch(Exception exp){
            throw new AuraHandledException(exp.getMessage());
        }
        return result;
    }
    
    /**
    * @description Returns Category and Subcategory filter values
    * @params insightList
    * @return List<Category> 
    **/
    public static List<Category> getCategoryFilterValues(List<Insight__c> insightList){
        LOGGER.info('OSB_SN_Notification_CTRL:getCategoryFilterValues initiated');
        List<Category> categoryList = new List<Category>();
        if(!insightList.isEmpty()){
            Map<String,Set<String>> categorySubCategoryMap = new Map<String,Set<String>>();
            for(Insight__c insightRecord : insightList){
                if(categorySubCategoryMap.containsKey(insightRecord.Category__c)){
                    categorySubCategoryMap.get(insightRecord.Category__c).add(insightRecord.Sub_Category__c);
                }
                else{
                    categorySubCategoryMap.put(insightRecord.Category__c,new Set<String>{insightRecord.Sub_Category__c});
                }
            }   
            for(String category : categorySubCategoryMap.keySet()){
                Category categoryOption = new Category();
                categoryOption.name = category;
                categoryOption.isSelected = true;
                List<SubCategory> subCategoryList = new List<SubCategory>();
                for(String subCategry : categorySubCategoryMap.get(category)){
                    SubCategory subCategoryOption = new SubCategory();
                    subCategoryOption.isSelected = true;
                    subCategoryOption.name = subCategry;
                    subCategoryList.add(subCategoryOption);
                }
                categoryOption.subCategories = subCategoryList;
                categoryList.add(categoryOption);
            }
        }
        return categoryList;
    }
    
    /**
    * @description Updates the nudge by setting  is Snoozed
    * @param nudgeId 
    * @param isSnoozed 
    * @param leadId 
    **/
    @AuraEnabled
    public static void saveOrUnsaveNudge(String nudgeId,Boolean isSnoozed,String leadId) {
        LOGGER.info('OSB_SN_Notification_CTRL:saveOrUnsaveNudge initiated');
        try{
            Insight__c insightRecord = new Insight__c();
            insightRecord.Id = nudgeId;
            insightRecord.Is_Snoozed__c = isSnoozed;
            Database.update(insightRecord);    
            LOGGER.debug('OSB_SN_Notification_CTRL:saveOrUnsaveNudge insight updated successfully');
            Insight_Action__c insightAction = createInsightActionRecord(nudgeId,'Is Snoozed');
            list<AKI_COMP_AkiliInsightsListviewController.WrapperInsightActions> wrapperInsightActionsList = new list<AKI_COMP_AkiliInsightsListviewController.WrapperInsightActions>();
            wrapperInsightActionsList.add(returnWrapperWithActionDetails(insightAction,leadId,isSnoozed));  
            String dataSet = JSON.serialize(wrapperInsightActionsList);
            LOGGER.debug('OSB_SN_Notification_CTRL:saveOrUnsaveNudge action details :'+dataSet);
            AKI_COMP_FutureCreateInsightAction.createInsightActionSendFeedback(dataSet);
            
        }catch(Exception exp){
            LOGGER.error('OSB_SN_Notification_CTRL : saveOrUnsaveNudge Exception logged: ',exp);
            throw new AuraHandledException(exp.getMessage());
        }        
    }
    
    /**
    * @description wrapper to hold insight list , filter options and unread insight ids 
    **/
    public class Result{
        @AuraEnabled public List<Insight__c> insightList;
        @AuraEnabled public List<Category> categories;
        @AuraEnabled public List<String> unreadInsightIds;
    }
    /**
    * @description wrapper to hold category name and its sub categories
    **/
    public class Category{
        @AuraEnabled public String name;
        @AuraEnabled public Boolean isSelected;
        @AuraEnabled public List<SubCategory> subCategories;
    }
    /**
    * @description wrapper to hold sub category name
    **/
    public class SubCategory{
        @AuraEnabled public String name;
        @AuraEnabled public Boolean isSelected;
    }
    
    /**
    * @description Create a new insight action record with actioned details and send feedback to smartnudge
    * @param insightId 
    * @param actionType
    * @param comments
    **/
    @AuraEnabled
    public static void createInsightActionAndSendFeedbackToSmartNudge(String insightId,String actionType,String leadId){
        LOGGER.info('OSB_SN_Notification_CTRL:createInsightActionAndSendFeedbackToSmartNudge initiated');
        try{     
            Insight_Action__c insightAction = createInsightActionRecord(insightId,actionType);
            LOGGER.debug('OSB_SN_Notification_CTRL :createInsightActionAndSendFeedbackToSmartNudge insight action created successfully');
            list<AKI_COMP_AkiliInsightsListviewController.WrapperInsightActions> wrapperInsightActionsList = New list<AKI_COMP_AkiliInsightsListviewController.WrapperInsightActions>();
            wrapperInsightActionsList.add(returnWrapperWithActionDetails(insightAction,leadId,insightAction.Is_Snooze_Value__c));  
            String dataSet = JSON.serialize(wrapperInsightActionsList);
            AKI_COMP_FutureCreateInsightAction.createInsightActionSendFeedback(dataSet);
            LOGGER.debug('OSB_SN_Notification_CTRL :createInsightActionAndSendFeedbackToSmartNudge action details sent to smartnudge successfully');
        }catch(Exception exp){
            LOGGER.error('OSB_SN_Notification_CTRL :createInsightActionAndSendFeedbackToSmartNudge Exception logged: ',exp);
            throw new AuraHandledException(exp.getMessage());
        }
    }
    /**
    * @description prepare wrapper record with actioned details
    * @param insightAction 
    * @param leadId
    * @param isSnoozed
    * @return wrapper record
    **/
    public static AKI_COMP_AkiliInsightsListviewController.WrapperInsightActions returnWrapperWithActionDetails(Insight_Action__c insightAction,String leadId,Boolean isSnoozed){
        LOGGER.info('OSB_SN_Notification_CTRL:returnWrapperWithActionDetails initiated');
        AKI_COMP_AkiliInsightsListviewController.WrapperInsightActions instWrap = new AKI_COMP_AkiliInsightsListviewController.WrapperInsightActions();
        instWrap.actionType = insightAction.Action_Type__c;
        instWrap.actionedUser = insightAction.Contact__c;
        instWrap.insightId = insightAction.Insight__c;
        instWrap.extleadId = leadId;   
        if(insightAction.Action_Type__c == 'Is Snoozed'){
            instWrap.actionValue = isSnoozed ? '1' : '0';            
        }
        instWrap.insightActId = insightAction.Id;
        return instWrap;
    }
    
    /**
    * @description Create a new insight action record with actioned details
    * @param insightId 
    * @param actionType
    * @return Insight_Action__c
    **/
    @AuraEnabled
    public static Insight_Action__c createInsightActionRecord(String insightId,String actionType){
        LOGGER.info('OSB_SN_Notification_CTRL:createInsightActionRecord initiated');
        Insight_Action__c insightAction = new Insight_Action__c();
        try{            
            List<User> userList = new SEL_Users().selectByIdWithRoleName(new Set<Id>{UserInfo.getUserId()});
            insightAction.Action_Type__c = actionType;
            insightAction.User__c = userList[0].Id;
            insightAction.Insight__c = insightId;
            insightAction.Contact__c = userList[0].ContactId;
            insightAction.Role__c = userList[0].UserRole.Name;
            insightAction.Reason__c = '';
            Database.insert(insightAction);
            LOGGER.debug('OSB_SN_Notification_CTRL:createInsightActionRecord insight action record created successfully');
        }catch(Exception exp){
            LOGGER.error('OSB_SN_Notification_CTRL :createInsightActionRecord Exception logged: ',exp);
            throw new AuraHandledException(exp.getMessage());
        }
        return insightAction;
    }
    
    /**
    * @description Update the nudge as read
    * @param nudgeId 
    * @param leadId
    **/
    @AuraEnabled
    public static void markNudgeAsRead(String nudgeId,String leadId) {
        LOGGER.info('OSB_SN_Notification_CTRL:markNudgeAsRead initiated');
        try{
            List<User> userList = new SEL_Users().selectById(new Set<Id>{UserInfo.getUserId()});
            List<Insight_Client_Relationship__c> insightClientRelationshipList = new SEL_InsightClientRelationships().selectByContactIdAndInsightIdWoSharing(new Set<String>{userList[0].ContactId},new Set<String>{nudgeId}); 
            LOGGER.debug('OSB_SN_Notification_CTRL:markNudgeAsRead insight client relationship size :'+insightClientRelationshipList.size());
            
            if(!insightClientRelationshipList.isEmpty() && insightClientRelationshipList[0].Is_Unread__c){
                insightClientRelationshipList[0].Is_Unread__c = false;
                Database.update(insightClientRelationshipList[0]);
                createInsightActionAndSendFeedbackToSmartNudge(nudgeId,'Nudge Read',leadId); 
                LOGGER.debug('OSB_SN_Notification_CTRL:markNudgeAsRead action details sent to smartnudge successfully');
            }
            
        }catch(Exception exp){
            LOGGER.error('OSB_SN_Notification_CTRL :markNudgeAsRead Exception logged: ',exp);
            throw new AuraHandledException(exp.getMessage());
        }
    }
    
    /**
* @description Update insight records
* @param insightList 
**
public static void updateInsights(List<Insight__c> insightList){
try{
Database.update(insightList);
}catch(DMLException exp){
LOGGER.error('OSB_SN_Notification_CTRL :updateInsights Exception logged: ',exp);
throw new CustomException(exp.getMessage()); 
}
}

/**
* @description Update insight client relationship records
* @param insightClientRelationshipRecords 
*
public static void updateInsightClientRelationshipRecords(List<Insight_Client_Relationship__c> insightClientRelationshipRecords){
try{
Database.update(insightClientRelationshipRecords);
}catch(DMLException exp){
LOGGER.error('OSB_SN_Notification_CTRL :updateInsights Exception logged: ',exp);
throw new CustomException(exp.getMessage());
}
}

/*******
* @description custom exception class
******
private class CustomException extends Exception {}

/**
* @description Is used for omitting sharing setting, when needed
*
private without sharing class WithoutSharing{
/**
* @description update Insight Client Relationship records without sharing
* @param insightClientRelationshipRecords
*
public void updateInsightClientRelationshipRecords( List<Insight_Client_Relationship__c> insightClientRelationshipRecords) {
OSB_SN_Notification_CTRL.updateInsightClientRelationshipRecords(insightClientRelationshipRecords);
}
/**
* @description update Insight records without sharing
* @param insightList
*
public void updateInsights( List<Insight__c> insightList) {
Database.update(insightList);
// OSB_SN_Notification_CTRL.updateInsights(insightList);
}
/**
* @description update Insight records without sharing
* @param insightList
*
public void createInsightActionRecords( List<Insight__c> insightList) {
Database.update(insightList);
// OSB_SN_Notification_CTRL.updateInsights(insightList);
}
}
*/
    
}