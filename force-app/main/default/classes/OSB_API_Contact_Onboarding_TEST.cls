/**
 * @description This is the test class for the OSB_API_Contact_Onboarding Rest Web Service Class.
 *
 * <AUTHOR>
 * @description Test Class for OSB_API_Contact_Onboarding.
 * @UserStory SFP-41159
 * @date March 2024
 */
@isTest
private class OSB_API_Contact_Onboarding_TEST {
    private static final String USER_EMAIL = '<EMAIL>';
    private static final String USER_FIRST_NAME = 'userFirstName';
    private static final String USER_LAST_NAME = 'userLastName';
    private static final String USER_TITLE = 'Tester';
    private static final String USER_COMPANY = 'userCompany';
    private static final String USER_PHONE = '*********';
    private static final String USER_COUNTRY = 'South Africa';
    private static final String ACCOUNT_CIF = 'cifTester';
    private static final String USER_SOLUTION = 'userSolution';
    private static final String USER_PING_ID = 'userID';
    private static final Integer POSITIVE_STATUS_CODE = 200;
    private static final Integer NEGATIVE_STATUS_CODE = 400;

    @TestSetup
    static void testDataSetUp() {
        List<OSB_URLs__c> osbURLsList = TEST_DataFactory.getOsbUrls();
        insert osbURLsList;
    }

    @isTest
    static void testNoContactExistingPingNoEmailToBeSentOutExistingSubscribedSolutions() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        fflib_ISObjectUnitOfWork uowMock = new fflib_SObjectMocks.SObjectUnitOfWork(
            mocks
        );
        SEL_Accounts selectorAccountMock = (SEL_Accounts) mocks.mock(
            SEL_Accounts.class
        );
        SEL_Contacts selectorContactMock = (SEL_Contacts) mocks.mock(
            SEL_Contacts.class
        );
        SEL_KnowledgeArticleVersions selectorKnowledgeMock = (SEL_KnowledgeArticleVersions) mocks.mock(
            SEL_KnowledgeArticleVersions.class
        );
        SEL_SubscribedSolutions selectorSubscribedSolutionMock = (SEL_SubscribedSolutions) mocks.mock(
            SEL_SubscribedSolutions.class
        );
        ORG_Application.unitOfWork.setMock(uowMock);
        OSB_SRV_PingIntegration servicePingMock = (OSB_SRV_PingIntegration) mocks.mock(
            OSB_SRV_PingIntegration.class
        );
        RestResponse res = new RestResponse();

        Contact testContact = (Contact) new BLD_Contact()
            .name(USER_FIRST_NAME, USER_LAST_NAME)
            .email(USER_EMAIL)
            .phone(USER_PHONE)
            .operatingCountry(USER_COUNTRY)
            .mock();

        Account testAccount = new Account();
        testAccount.Name = 'TesterAccount';
        testAccount.CIF__c = ACCOUNT_CIF;
        insert testAccount;

        Knowledge__kav testSolution = (Knowledge__kav) new BLD_Knowledge()
            .setOSBData()
            .isComingSoon(false)
            .useSolution()
            .mock();

        Subscribed_Solutions__c testSubscribedSolution = (Subscribed_Solutions__c) new BLD_Subscribed_Solutions()
            .mock();

        mocks.startStubbing();
        mocks.when(selectorAccountMock.sObjectType())
            .thenReturn(Account.SObjectType);
        mocks.when(
                selectorAccountMock.selectById(
                    (Set<ID>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Account>{ testAccount });
        mocks.when(
                selectorAccountMock.selectByCIFNumber(
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Account>{ testAccount });
        mocks.when(selectorContactMock.sObjectType())
            .thenReturn(Contact.SObjectType);
        mocks.when(
                selectorContactMock.selectByEmail(
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Contact>());
        mocks.when(servicePingMock.getUser((String) fflib_Match.anyString()))
            .thenReturn(USER_PING_ID);
        mocks.when(selectorKnowledgeMock.sObjectType())
            .thenReturn(Knowledge__kav.SObjectType);
        mocks.when(
                selectorKnowledgeMock.selectByPublishStatusAndTitleWoSharing(
                    (Set<String>) fflib_Match.anyObject(),
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Knowledge__kav>{ testSolution });
        mocks.when(selectorSubscribedSolutionMock.sObjectType())
            .thenReturn(Subscribed_Solutions__c.SObjectType);
        mocks.when(
                selectorSubscribedSolutionMock.selectByContactIdAndSolutionId(
                    (Set<ID>) fflib_Match.anyObject(),
                    (Set<ID>) fflib_Match.anyObject()
                )
            )
            .thenReturn(
                new List<Subscribed_Solutions__c>{ testSubscribedSolution }
            );
        mocks.stopStubbing();

        ORG_Application.selector.setMock(selectorAccountMock);
        ORG_Application.selector.setMock(selectorContactMock);
        ORG_Application.selector.setMock(selectorKnowledgeMock);
        ORG_Application.selector.setMock(selectorSubscribedSolutionMock);
        ORG_Application.service.setMock(
            OSB_SRV_PingIntegration.IService.class,
            servicePingMock
        );

        String serializedRequestBody = JSON.serialize(
            generateRequestBody(testContact, true)
        );

        generateRequest(serializedRequestBody);
        RestContext.response = res;

        Test.startTest();
        OSB_API_Contact_Onboarding.completeContactOnboarding();
        Test.stopTest();

        fflib_ArgumentCaptor argument = fflib_ArgumentCaptor.forClass(
            fflib_ISObjectUnitOfWork.class
        );
        ((fflib_ISObjectUnitOfWork) mocks.verify(uowMock, 1))
            .registerNew((Contact) argument.capture());
        Contact createdContact = (Contact) argument.getValue();

        Assert.areEqual(
            POSITIVE_STATUS_CODE,
            res.statusCode,
            'Response Status Code is 200.'
        );
        Assert.areEqual(
            USER_EMAIL,
            createdContact.Email,
            'Contact Created with correct details.'
        );
        Assert.areEqual(
            DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_APPROVED,
            createdContact.OSB_Community_Access_Status__c,
            'Contact status set to approved since Ping ID exists.'
        );
    }

    @isTest
    static void testExistingContactNoPingnNoEmailToBeSentOutExistingSubscribedSolutions() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        fflib_ISObjectUnitOfWork uowMock = new fflib_SObjectMocks.SObjectUnitOfWork(
            mocks
        );
        SEL_Accounts selectorAccountMock = (SEL_Accounts) mocks.mock(
            SEL_Accounts.class
        );
        SEL_Contacts selectorContactMock = (SEL_Contacts) mocks.mock(
            SEL_Contacts.class
        );
        SEL_KnowledgeArticleVersions selectorKnowledgeMock = (SEL_KnowledgeArticleVersions) mocks.mock(
            SEL_KnowledgeArticleVersions.class
        );
        SEL_SubscribedSolutions selectorSubscribedSolutionMock = (SEL_SubscribedSolutions) mocks.mock(
            SEL_SubscribedSolutions.class
        );
        ORG_Application.unitOfWork.setMock(uowMock);
        OSB_SRV_PingIntegration servicePingMock = (OSB_SRV_PingIntegration) mocks.mock(
            OSB_SRV_PingIntegration.class
        );
        RestResponse res = new RestResponse();

        Contact testContact = (Contact) new BLD_Contact()
            .name(USER_FIRST_NAME, USER_LAST_NAME)
            .email(USER_EMAIL)
            .phone(USER_PHONE)
            .operatingCountry(USER_COUNTRY)
            .mock();

        Account testAccount = new Account();
        testAccount.Name = 'TesterAccount';
        testAccount.CIF__c = ACCOUNT_CIF;
        insert testAccount;

        Knowledge__kav testSolution = (Knowledge__kav) new BLD_Knowledge()
            .setOSBData()
            .isComingSoon(false)
            .useSolution()
            .mock();

        mocks.startStubbing();
        mocks.when(selectorAccountMock.sObjectType())
            .thenReturn(Account.SObjectType);
        mocks.when(
                selectorAccountMock.selectById(
                    (Set<ID>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Account>{ testAccount });
        mocks.when(selectorContactMock.sObjectType())
            .thenReturn(Contact.SObjectType);
        mocks.when(
                selectorContactMock.selectByEmail(
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Contact>{ testContact });
        mocks.when(servicePingMock.getUser((String) fflib_Match.anyString()))
            .thenReturn('');
        mocks.when(selectorKnowledgeMock.sObjectType())
            .thenReturn(Knowledge__kav.SObjectType);
        mocks.when(
                selectorKnowledgeMock.selectByPublishStatusAndTitleWoSharing(
                    (Set<String>) fflib_Match.anyObject(),
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Knowledge__kav>{ testSolution });
        mocks.when(selectorSubscribedSolutionMock.sObjectType())
            .thenReturn(Subscribed_Solutions__c.SObjectType);
        mocks.when(
                selectorSubscribedSolutionMock.selectByContactIdAndSolutionId(
                    (Set<ID>) fflib_Match.anyObject(),
                    (Set<ID>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Subscribed_Solutions__c>());
        mocks.stopStubbing();

        ORG_Application.selector.setMock(selectorAccountMock);
        ORG_Application.selector.setMock(selectorContactMock);
        ORG_Application.selector.setMock(selectorKnowledgeMock);
        ORG_Application.selector.setMock(selectorSubscribedSolutionMock);
        ORG_Application.service.setMock(
            OSB_SRV_PingIntegration.IService.class,
            servicePingMock
        );

        String serializedRequestBody = JSON.serialize(
            generateRequestBody(testContact, false)
        );

        generateRequest(serializedRequestBody);
        RestContext.response = res;

        Test.startTest();
        OSB_API_Contact_Onboarding.completeContactOnboarding();
        Test.stopTest();

        fflib_ArgumentCaptor argument = fflib_ArgumentCaptor.forClass(
            fflib_ISObjectUnitOfWork.class
        );
        fflib_ArgumentCaptor argumentSub = fflib_ArgumentCaptor.forClass(
            fflib_ISObjectUnitOfWork.class
        );
        ((fflib_ISObjectUnitOfWork) mocks.verify(uowMock, 2))
            .registerDirty((Contact) argument.capture());
        Contact createdContact = (Contact) argument.getValue();
        ((fflib_ISObjectUnitOfWork) mocks.verify(uowMock, 1))
            .registerNew((Subscribed_Solutions__c) argumentSub.capture());
        Subscribed_Solutions__c createdSubscribedSolution = (Subscribed_Solutions__c) argumentSub.getValue();

        Assert.areEqual(
            POSITIVE_STATUS_CODE,
            res.statusCode,
            'Response Status Code is 200.'
        );
        Assert.areEqual(
            USER_EMAIL,
            createdContact.Email,
            'Contact Created with correct details.'
        );
        Assert.areEqual(
            DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_INVITE_SENT_VIA_URL,
            createdContact.OSB_Community_Access_Status__c,
            'Contact status set to invite sent via url since no PING ID found.'
        );
        Assert.areEqual(
            testSolution.Id,
            createdSubscribedSolution.Solution__c,
            'Solution was added to the contact for their dashboard.'
        );
    }

    @isTest
    static void testExistingContactExistingPingnEmailNotToBeSentOutExistingSubscribedSolution() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        fflib_ISObjectUnitOfWork uowMock = new fflib_SObjectMocks.SObjectUnitOfWork(
            mocks
        );
        SEL_Contacts selectorContactMock = (SEL_Contacts) mocks.mock(
            SEL_Contacts.class
        );
        SEL_KnowledgeArticleVersions selectorKnowledgeMock = (SEL_KnowledgeArticleVersions) mocks.mock(
            SEL_KnowledgeArticleVersions.class
        );
        SEL_SubscribedSolutions selectorSubscribedSolutionMock = (SEL_SubscribedSolutions) mocks.mock(
            SEL_SubscribedSolutions.class
        );
        ORG_Application.unitOfWork.setMock(uowMock);
        OSB_SRV_PingIntegration servicePingMock = (OSB_SRV_PingIntegration) mocks.mock(
            OSB_SRV_PingIntegration.class
        );
        RestResponse res = new RestResponse();

        Contact testContact = (Contact) new BLD_Contact()
            .name(USER_FIRST_NAME, USER_LAST_NAME)
            .email(USER_EMAIL)
            .phone(USER_PHONE)
            .operatingCountry(USER_COUNTRY)
            .mock();

        Knowledge__kav testSolution = (Knowledge__kav) new BLD_Knowledge()
            .setOSBData()
            .isComingSoon(false)
            .useSolution()
            .mock();

        Subscribed_Solutions__c testSubscribedSolution = (Subscribed_Solutions__c) new BLD_Subscribed_Solutions()
            .mock();

        mocks.startStubbing();
        mocks.when(selectorContactMock.sObjectType())
            .thenReturn(Contact.SObjectType);
        mocks.when(
                selectorContactMock.selectByEmail(
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Contact>{ testContact });
        mocks.when(servicePingMock.getUser((String) fflib_Match.anyString()))
            .thenReturn(USER_PING_ID);
        mocks.when(selectorKnowledgeMock.sObjectType())
            .thenReturn(Knowledge__kav.SObjectType);
        mocks.when(
                selectorKnowledgeMock.selectByPublishStatusAndTitleWoSharing(
                    (Set<String>) fflib_Match.anyObject(),
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Knowledge__kav>{ testSolution });
        mocks.when(selectorSubscribedSolutionMock.sObjectType())
            .thenReturn(Subscribed_Solutions__c.SObjectType);
        mocks.when(
                selectorSubscribedSolutionMock.selectByContactIdAndSolutionId(
                    (Set<ID>) fflib_Match.anyObject(),
                    (Set<ID>) fflib_Match.anyObject()
                )
            )
            .thenReturn(
                new List<Subscribed_Solutions__c>{ testSubscribedSolution }
            );
        mocks.stopStubbing();

        ORG_Application.selector.setMock(selectorContactMock);
        ORG_Application.selector.setMock(selectorKnowledgeMock);
        ORG_Application.selector.setMock(selectorSubscribedSolutionMock);
        ORG_Application.service.setMock(
            OSB_SRV_PingIntegration.IService.class,
            servicePingMock
        );

        String serializedRequestBody = JSON.serialize(
            generateRequestBody(testContact, false)
        );

        generateRequest(serializedRequestBody);
        RestContext.response = res;

        Test.startTest();
        OSB_API_Contact_Onboarding.completeContactOnboarding();
        Test.stopTest();

        fflib_ArgumentCaptor argument = fflib_ArgumentCaptor.forClass(
            fflib_ISObjectUnitOfWork.class
        );
        ((fflib_ISObjectUnitOfWork) mocks.verify(uowMock, 1))
            .registerDirty((Contact) argument.capture());
        Contact existingContact = (Contact) argument.getValue();

        Assert.areEqual(
            POSITIVE_STATUS_CODE,
            res.statusCode,
            'Response Status Code is 200.'
        );
        Assert.areEqual(
            USER_EMAIL,
            existingContact.Email,
            'Contact Exists with correct details.'
        );
        Assert.areEqual(
            DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_APPROVED,
            existingContact.OSB_Community_Access_Status__c,
            'Existing Contact status set to approved since Ping ID exists.'
        );
    }

    @isTest
    static void testExistingInActiveContact() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        fflib_ISObjectUnitOfWork uowMock = new fflib_SObjectMocks.SObjectUnitOfWork(
            mocks
        );
        SEL_Contacts selectorContactMock = (SEL_Contacts) mocks.mock(
            SEL_Contacts.class
        );
        SEL_KnowledgeArticleVersions selectorKnowledgeMock = (SEL_KnowledgeArticleVersions) mocks.mock(
            SEL_KnowledgeArticleVersions.class
        );

        ORG_Application.unitOfWork.setMock(uowMock);

        RestResponse res = new RestResponse();

        Contact testContact = (Contact) new BLD_Contact()
            .name(USER_FIRST_NAME, USER_LAST_NAME)
            .email(USER_EMAIL)
            .phone(USER_PHONE)
            .operatingCountry(USER_COUNTRY)
            .inactive(true)
            .mock();

        Knowledge__kav testSolution = (Knowledge__kav) new BLD_Knowledge()
            .setOSBData()
            .isComingSoon(false)
            .useSolution()
            .mock();

        mocks.startStubbing();
        mocks.when(selectorContactMock.sObjectType())
            .thenReturn(Contact.SObjectType);
        mocks.when(
                selectorContactMock.selectByEmail(
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Contact>{ testContact });
        mocks.when(selectorKnowledgeMock.sObjectType())
            .thenReturn(Knowledge__kav.SObjectType);
        mocks.when(
                selectorKnowledgeMock.selectByPublishStatusAndTitleWoSharing(
                    (Set<String>) fflib_Match.anyObject(),
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Knowledge__kav>{ testSolution });

        mocks.stopStubbing();

        ORG_Application.selector.setMock(selectorContactMock);
        ORG_Application.selector.setMock(selectorKnowledgeMock);

        String serializedRequestBody = JSON.serialize(
            generateRequestBody(testContact, false)
        );

        generateRequest(serializedRequestBody);
        RestContext.response = res;

        Test.startTest();
        OSB_API_Contact_Onboarding.completeContactOnboarding();
        Test.stopTest();

        Map<String, Object> responseBodyMap = (Map<String, Object>) JSON.deserializeUntyped(
            res.responseBody.toString()
        );

        Assert.areEqual(
            POSITIVE_STATUS_CODE,
            res.statusCode,
            'Response Status Code is 200.'
        );

        Assert.areEqual(
            DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_INACTIVE,
            responseBodyMap.get('status'),
            'Response Body contains an inactive status for the person.'
        );
    }

    @isTest
    static void testMissingDataError() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        fflib_ISObjectUnitOfWork uowMock = new fflib_SObjectMocks.SObjectUnitOfWork(
            mocks
        );
        SEL_Contacts selectorContactMock = (SEL_Contacts) mocks.mock(
            SEL_Contacts.class
        );
        ORG_Application.unitOfWork.setMock(uowMock);
        RestResponse res = new RestResponse();

        Contact testContact = (Contact) new BLD_Contact()
            .operatingCountry(USER_COUNTRY)
            .mock();

        mocks.startStubbing();
        mocks.when(selectorContactMock.sObjectType())
            .thenReturn(Contact.SObjectType);
        mocks.when(
                selectorContactMock.selectByEmail(
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Contact>());
        mocks.stopStubbing();

        ORG_Application.selector.setMock(selectorContactMock);

        String serializedRequestBody = JSON.serialize(
            generateRequestBody(testContact, false)
        );

        generateRequest(serializedRequestBody);
        RestContext.response = res;

        Test.startTest();
        OSB_API_Contact_Onboarding.completeContactOnboarding();
        Test.stopTest();

        Assert.areEqual(
            NEGATIVE_STATUS_CODE,
            res.statusCode,
            'Response Status Code is 400.'
        );
        Assert.areEqual(
            true,
            res.responseBody.toString()
                .contains(OSB_API_Contact_Onboarding.MISSING_DATA),
            'Response Error Code is missing data.'
        );
    }

    @isTest
    static void testMissingDataEmailFormatError() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        fflib_ISObjectUnitOfWork uowMock = new fflib_SObjectMocks.SObjectUnitOfWork(
            mocks
        );
        SEL_Contacts selectorContactMock = (SEL_Contacts) mocks.mock(
            SEL_Contacts.class
        );
        ORG_Application.unitOfWork.setMock(uowMock);
        RestResponse res = new RestResponse();

        Contact testContact = (Contact) new BLD_Contact()
            .name(USER_FIRST_NAME, USER_LAST_NAME)
            .email(USER_FIRST_NAME)
            .phone(USER_PHONE)
            .operatingCountry(USER_COUNTRY)
            .mock();

        mocks.startStubbing();
        mocks.when(selectorContactMock.sObjectType())
            .thenReturn(Contact.SObjectType);
        mocks.when(
                selectorContactMock.selectByEmail(
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Contact>());
        mocks.stopStubbing();

        ORG_Application.selector.setMock(selectorContactMock);

        String serializedRequestBody = JSON.serialize(
            generateRequestBody(testContact, false)
        );

        generateRequest(serializedRequestBody);
        RestContext.response = res;

        Test.startTest();
        OSB_API_Contact_Onboarding.completeContactOnboarding();
        Test.stopTest();

        Assert.areEqual(
            NEGATIVE_STATUS_CODE,
            res.statusCode,
            'Response Status Code is 400.'
        );
        Assert.areEqual(
            true,
            res.responseBody.toString().contains('email is not a valid format'),
            'Response Error Code is missing data due to email not being in the correct format.'
        );
    }

    @isTest
    static void testAccountDoesNotExistError() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        fflib_ISObjectUnitOfWork uowMock = new fflib_SObjectMocks.SObjectUnitOfWork(
            mocks
        );
        SEL_Accounts selectorAccountMock = (SEL_Accounts) mocks.mock(
            SEL_Accounts.class
        );
        SEL_Contacts selectorContactMock = (SEL_Contacts) mocks.mock(
            SEL_Contacts.class
        );
        SEL_KnowledgeArticleVersions selectorKnowledgeMock = (SEL_KnowledgeArticleVersions) mocks.mock(
            SEL_KnowledgeArticleVersions.class
        );
        ORG_Application.unitOfWork.setMock(uowMock);
        OSB_SRV_PingIntegration servicePingMock = (OSB_SRV_PingIntegration) mocks.mock(
            OSB_SRV_PingIntegration.class
        );
        RestResponse res = new RestResponse();

        Contact testContact = (Contact) new BLD_Contact()
            .name(USER_FIRST_NAME, USER_LAST_NAME)
            .email(USER_EMAIL)
            .phone(USER_PHONE)
            .operatingCountry(USER_COUNTRY)
            .mock();

        Knowledge__kav testSolution = (Knowledge__kav) new BLD_Knowledge()
            .setOSBData()
            .isComingSoon(false)
            .useSolution()
            .mock();

        mocks.startStubbing();
        mocks.when(selectorAccountMock.sObjectType())
            .thenReturn(Account.SObjectType);
        mocks.when(
                selectorAccountMock.selectByCIFNumber(
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Account>());
        mocks.when(selectorContactMock.sObjectType())
            .thenReturn(Contact.SObjectType);
        mocks.when(
                selectorContactMock.selectByEmail(
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Contact>());
        mocks.when(servicePingMock.getUser((String) fflib_Match.anyString()))
            .thenReturn(USER_PING_ID);
        mocks.when(selectorKnowledgeMock.sObjectType())
            .thenReturn(Knowledge__kav.SObjectType);
        mocks.when(
                selectorKnowledgeMock.selectByPublishStatusAndTitleWoSharing(
                    (Set<String>) fflib_Match.anyObject(),
                    (Set<String>) fflib_Match.anyObject()
                )
            )
            .thenReturn(new List<Knowledge__kav>{ testSolution });
        mocks.stopStubbing();

        ORG_Application.selector.setMock(selectorAccountMock);
        ORG_Application.selector.setMock(selectorContactMock);
        ORG_Application.selector.setMock(selectorKnowledgeMock);
        ORG_Application.service.setMock(
            OSB_SRV_PingIntegration.IService.class,
            servicePingMock
        );

        String serializedRequestBody = JSON.serialize(
            generateRequestBody(testContact, false)
        );

        generateRequest(serializedRequestBody);
        RestContext.response = res;

        Test.startTest();
        OSB_API_Contact_Onboarding.completeContactOnboarding();
        Test.stopTest();

        Assert.areEqual(
            NEGATIVE_STATUS_CODE,
            res.statusCode,
            'Response Status Code is 400.'
        );
        Assert.areEqual(
            true,
            res.responseBody.toString()
                .contains(OSB_API_Contact_Onboarding.ACCOUNT_DOES_NOT_EXIST),
            'Response Error Code is account does not exists.'
        );
    }

    private static Map<String, Object> generateRequestBody(
        Contact contactDetials,
        Boolean sendEmail
    ) {
        Map<String, Object> requestBody = new Map<String, Object>();
        requestBody.put('firstName', contactDetials.FirstName);
        requestBody.put('lastName', contactDetials.LastName);
        requestBody.put('email', contactDetials.Email);
        requestBody.put('cellPhoneNumber', contactDetials.phone);
        requestBody.put('jobTitle', USER_TITLE);
        requestBody.put('cifNumber', ACCOUNT_CIF);
        requestBody.put('companyName', USER_COMPANY);
        requestBody.put('appName', USER_SOLUTION);
        requestBody.put(
            'operatingCountry',
            contactDetials.OSB_Operating_Country__c
        );
        requestBody.put('sendEmail', sendEmail);

        return requestBody;
    }

    private static void generateRequest(String serializedRequestBody) {
        RestRequest request = new RestRequest();
        request.requestURI = '/services/apexrest/onehub-users/*';
        request.httpMethod = 'POST';
        request.requestBody = Blob.valueOf(serializedRequestBody);
        RestContext.request = request;
    }
}
