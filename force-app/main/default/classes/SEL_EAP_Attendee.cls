/**
 * @description       : Selector class for EAP_Attendee__c SObject
 * <AUTHOR> TCK
 * @group             :
 * @last modified on  : 07-21-2022
 * @last modified by  : TCK
 **/
public without sharing class SEL_EAP_Attendee extends fflib_SObjectSelector {
    /**
     * @description
     * <AUTHOR> | 06-13-2022
     * @return List<Schema.SObjectField>
     **/
    public List<Schema.SObjectField> getSObjectFieldList() {
        return new List<Schema.SObjectField>{
            EAP_Attendee__c.Id,
            EAP_Attendee__c.Name,
            EAP_Attendee__c.EAP_AppEvent__c,
            EAP_Attendee__c.EAP_Contact__c,
            EAP_Attendee__c.EAP_RoleEvent__c,
            EAP_Attendee__c.EAP_ContactName__c,
            EAP_Attendee__c.EAP_Contact_Email__c,
            EAP_Attendee__c.EAP_AttendeeCompany__c
        };
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @return Schema.SObjectType
     **/
    public Schema.SObjectType getSObjectType() {
        return EAP_Attendee__c.sObjectType;
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @return SEL_EAP_Attendee
     **/
    public static SEL_EAP_Attendee newInstance() {
        return (SEL_EAP_Attendee) ORG_Application.selector.newInstance(
            EAP_Attendee__c.SObjectType
        );
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @param ids
     * @return List<EAP_Attendee__c>
     **/
    public List<EAP_Attendee__c> selectById(Set<Id> ids) {
        return (List<EAP_Attendee__c>) Database.query(
            newQueryFactory().setCondition('Id IN: ids').toSOQL()
        );
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @param ids
     * @return List<EAP_Attendee__c>
     **/
    public List<EAP_Attendee__c> selectByIds(List<Id> ids) {
        return (List<EAP_Attendee__c>) Database.query(
            newQueryFactory().setCondition('Id IN: ids').toSOQL()
        );
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @param id
     * @return List<EAP_Attendee__c>
     **/
    public List<EAP_Attendee__c> selectByContactId(Set<Id> id) {
        return (List<EAP_Attendee__c>) Database.query(
            newQueryFactory().setCondition('EAP_Contact__c =: id').toSOQL()
        );
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @param eventId
     * @return List<EAP_Attendee__c>
     **/
    public List<EAP_Attendee__c> selectByEventId(String eventId) {
        return (List<EAP_Attendee__c>) Database.query(
            newQueryFactory()
                .setCondition('EAP_AppEvent__c =: eventId')
                .toSOQL()
        );
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @param eventId
     * @param contactId
     * @return List<EAP_Attendee__c>
     **/
    public List<EAP_Attendee__c> selectByEventIdAndContactSync(
        String eventId,
        String contactId
    ) {
        return (List<EAP_Attendee__c>) Database.query(
            newQueryFactory()
                .setCondition(
                    'EAP_AppEvent__c =: eventId AND EAP_Contact__c =: contactId'
                )
                .toSOQL()
        );
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @param nowDatetime
     * @param contactId
     * @return List<EAP_Attendee__c>
     **/
    public List<EAP_Attendee__c> selectByContactAndEventDate(
        Datetime nowDatetime,
        String contactId
    ) {
        return (List<EAP_Attendee__c>) Database.query(
            newQueryFactory()
                .setCondition(
                    'EAP_AppEvent__r.EAP_EndDate__c >: nowDatetime AND EAP_Contact__c =: contactId'
                )
                .toSOQL()
        );
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @param nowDatetime
     * @param contactId
     * @return List<EAP_Attendee__c>
     **/
    public List<EAP_Attendee__c> selectByContactAndEventDateAndFormCompleted(
        Datetime nowDatetime,
        String contactId
    ) {
        return (List<EAP_Attendee__c>) Database.query(
            newQueryFactory()
                .setCondition(
                    '(EAP_AppEvent__r.EAP_EndDate__c >: nowDatetime AND EAP_Contact__c =: contactId) AND (EAP_FormCompleted__c = false)'
                )
                .toSOQL()
        );
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @param nowDatetime
     * @param contactId
     * @return List<EAP_Attendee__c>
     **/
    public List<EAP_Attendee__c> selectByContactAndPastDate(
        Datetime nowDatetime,
        String contactId
    ) {
        return (List<EAP_Attendee__c>) Database.query(
            newQueryFactory()
                .setCondition(
                    'EAP_AppEvent__r.EAP_EndDate__c <=: nowDatetime AND EAP_Contact__c =: contactId'
                )
                .toSOQL()
        );
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @param eventId
     * @return List<EAP_Attendee__c>
     **/
    public List<EAP_Attendee__c> selectCorporatesByEventId(String eventId) {
        return (List<EAP_Attendee__c>) Database.query(
            newQueryFactory()
                .setCondition(
                    '(EAP_AppEvent__c =: eventId) AND (EAP_RoleEvent__c = \'Corporate\')'
                )
                .toSOQL()
        );
    }

    /**
     * @description
     * <AUTHOR> | 06-14-2022
     * @param eventId
     * @return List<EAP_Attendee__c>
     **/
    public List<EAP_Attendee__c> selectInvestorsByEventId(String eventId) {
        return (List<EAP_Attendee__c>) Database.query(
            newQueryFactory()
                .selectField('EAP_ContactName__c')
                .setCondition(
                    '(EAP_AppEvent__c =: eventId) AND (EAP_RoleEvent__c = \'Investor\')'
                )
                .toSOQL()
        );
    }
}
