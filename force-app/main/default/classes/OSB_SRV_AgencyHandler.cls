/**
 * @description Service Class for handling logic of Agency disclaimer
 *
 * <AUTHOR> (<EMAIL>)
 * @date September 2024
 * @UserStory SFP-36886
 */
public with sharing class OSB_SRV_AgencyHandler {

    private static final String AUTH_FORM_VALUE = 'OneHub | Agency Disclaimer';
    private static final sbgplatform.rflib_Logger LOGGER = sbgplatform.rflib_LoggerUtil.getFactory().createLogger('OSB_SRV_AgencyHandler');

    /**
	 * @description Gets the OneHub Agency Disclaimer Form
     *
     * @return AuthorizationForm Authorization Form
	 **/
    public static AuthorizationForm getAuthorizationForm() { 
        try {     
            List<AuthorizationForm> authForms = SEL_AuthorizationForm.newInstance()
            .selectByName(new Set<String>{ AUTH_FORM_VALUE });
            return getHighestRevisionForm(authForms);
        } catch (Exception e) {
            LOGGER.error('OSB_SRV_AgencyHandler : getAuthorizationForm - Exception logged: ', e);
            throw e;
        }
    }

    /**
	 * @description Returns the AuthorizationForm with the highest RevisionNumber from the provided list
     * @param authForms List<AuthorizationForm> list of Authorization forms
     *
     * @return Authorization with the highest RevisionNumber
	 **/
    public static AuthorizationForm getHighestRevisionForm(List<AuthorizationForm> authForms){
        try {     
            AuthorizationForm highestRevisionForm;
            Integer highestRevision = 0;

            for (AuthorizationForm form : authForms) {
                Integer currentRevision = Integer.valueOf(form.RevisionNumber);
                if(currentRevision > highestRevision) {
                    highestRevision = currentRevision;
                    highestRevisionForm = form;
                }
            }
            return highestRevisionForm;
        } catch (Exception e) {
            LOGGER.error('OSB_SRV_AgencyHandler : getHighestRevisionForm - Exception logged: ', e);
            throw e;
        }
    }

    /**
     * @description Retrieves the latest AuthorizationForm and determines if the contact's latest consents
     * are valid and match the current version of the form.
     *
     * @return Map<Id, Map<String, Object>>AuthorizationFormConsent Ids and their respective valid status.
     */
    public static Map<Id, Map<String, Object>> getConsentStatusForContact() {
        try {   
            Id userId = UserInfo.getUserID();
            Contact userContact = SEL_Contacts.newInstance().selectByUserId(new Set<Id>{ userId })[0];   

            AuthorizationForm authForm = getAuthorizationForm();
            Decimal currentRevisionVersion = Decimal.valueOf(authForm.RevisionNumber);
            Id currentAuthFormTextId = authForm.DefaultAuthFormTextId;
        
            List<AuthorizationFormConsent> consents = SEL_AuthorizationFormConsent.newInstance().selectByContactId(new Set<Id>{ userContact.Id });
            Map<Id, AuthorizationFormText> authFormTextMap = getAuthorizationFormTextRecords(consents);
            Map<Id, AuthorizationFormConsent> latestConsentPerKnowledge = findLatestConsents(consents, authFormTextMap);
        
            Map<Id, Map<String, Object>> consentStatusMap = new Map<Id, Map<String, Object>>();
        
            for (Id knowledgeId : latestConsentPerKnowledge.keySet()) {
                AuthorizationFormConsent latestConsent = latestConsentPerKnowledge.get(knowledgeId);
                AuthorizationFormText latestFormText = authFormTextMap.get(latestConsent.AuthorizationFormTextId);
        
                Boolean isVersionMatch = (latestFormText.TextVersion__c == currentRevisionVersion);
                Boolean isTextIdMatch = latestConsent.AuthorizationFormTextId == currentAuthFormTextId;

                Map<String, Object> consentInfo = new Map<String, Object>();
                Boolean isConsentExpired = !String.isBlank(String.valueOf(latestConsent.ConsentExpirationDateTime));
                
                consentInfo.put('isConsentExpired', isConsentExpired);
                if (!isConsentExpired) {
                    consentInfo.put('isLatestConsent', (isVersionMatch && isTextIdMatch));
                } else {
                    consentInfo.put('isLatestConsent', false);
                }
                consentInfo.put('knowledgeId', knowledgeId); 
                consentStatusMap.put(latestConsent.Id, consentInfo);
            }
            return consentStatusMap;
        } catch (Exception e) {
            LOGGER.error('OSB_SRV_AgencyHandler : getConsentStatusForContact - Exception logged: ', e);
            throw e;
        }
    }
    
    /**
     * @description Retrieves AuthorizationFormText records corresponding to the provided list of AuthorizationFormConsent records.
     * @param consents AuthorizationFormConsent details 
     * 
     * @return Map<Id, AuthorizationFormText> AuthorizationFormText Ids and their corresponding AuthorizationFormText records.
     */
    public static Map<Id, AuthorizationFormText> getAuthorizationFormTextRecords(List<AuthorizationFormConsent> consents) {
        Set<Id> authFormTextIds = new Set<Id>();
        for (AuthorizationFormConsent consent : consents) {
            if (consent.AuthorizationFormTextId != null) {
                authFormTextIds.add(consent.AuthorizationFormTextId);
            }
        }
        return new Map<Id, AuthorizationFormText>(SEL_AuthorizationFormText.newInstance().selectById(authFormTextIds));
    }

    /**
     * @description Finds the latest AuthorizationFormConsent for each Knowledge__c based on the highest TextVersion__c.
     * @param consents The list of AuthorizationFormConsent
     * @param authFormTextMap A map of AuthorizationFormText Ids and their corresponding AuthorizationFormText records.
     * 
     * @return Map<Id, AuthorizationFormConsent> Knowledge__c Ids to the latest AuthorizationFormConsent records.
     */
    public static Map<Id, AuthorizationFormConsent> findLatestConsents(
        List<AuthorizationFormConsent> consents, 
        Map<Id, AuthorizationFormText> authFormTextMap) {
        
        Map<Id, AuthorizationFormConsent> latestConsentPerKnowledge = new Map<Id, AuthorizationFormConsent>();
        Map<Id, Decimal> highestVersionPerKnowledge = new Map<Id, Decimal>();

        for (AuthorizationFormConsent consent : consents) { 
            if (!String.isBlank(consent.AuthorizationFormTextId)) {
                AuthorizationFormText authFormText = authFormTextMap.get(consent.AuthorizationFormTextId);
                Id knowledgeId = consent.Knowledge__c;
                Decimal consentedVersion = authFormText.TextVersion__c;
                DateTime consentCreatedDate = consent.CreatedDate;
                if (!highestVersionPerKnowledge.containsKey(knowledgeId) || 
                    consentedVersion > highestVersionPerKnowledge.get(knowledgeId) ||
                    (consentedVersion == highestVersionPerKnowledge.get(knowledgeId) && 
                    consentCreatedDate > latestConsentPerKnowledge.get(knowledgeId).CreatedDate)) 
                {
                    highestVersionPerKnowledge.put(knowledgeId, consentedVersion);
                    latestConsentPerKnowledge.put(knowledgeId, consent);
                }
            }
        }
        return latestConsentPerKnowledge;
    }
}