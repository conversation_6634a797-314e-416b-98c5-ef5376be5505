/**
 * @description       : US: SFP-11298 Add to calendar from email functionality
 * <AUTHOR> TCK
 * @group             : 
 * @last modified on  : 06-13-2022
 * @last modified by  : TCK
**/
@IsTest
private class SEL_OrgWideEmailAddress_TEST {
    @IsTest
    static void shouldSelectByAddress() {
        String address;
        Test.startTest();
        SEL_OrgWideEmailAddress.newInstance().selectByAddress(address);
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        System.assert(result.getCondition().containsIgnoreCase('Address =: address'), 'Query contains Address =: address');
    }
}