/**
 * @description Controller class for OSB_sendEmailPage Visualforce Page
 *
 * <AUTHOR> (<EMAIL>)
 * @date August 2020
 * 
 * <AUTHOR>
 * @description Update to sendApEmailInvite to autolaunch flow for email to be sent out. Update to updateContact to accomodate new field and contact retrieval modification
 * @UserStory SFP-41334
 * @date August 2024
 *
 **/
public class OSB_SendEmailPage_CTRL {
	public Id recordID {get;set;}
    public Contact contactRecord { get; set;}
    private static final sbgplatform.rflib_Logger LOGGER = sbgplatform.rflib_LoggerUtil.getFactory()
    .createLogger('OSB_SendEmailPage_CTRL');
    private static final string OSB_MAIL_ENCRKEY = 'EMAIL_ENCR';
    private static final String OSB_CUSTOM_SETTING_OSB_BASE_URL = 'OSB_Base_URL';
    private static final String OSB_AP_CONTACT_ONEHUB_ADMIN_INVITE_EMAIL_TEMPLATE = 'OSB_AP_Contact_OneHub_Admin_Invite';
    
    /**
     * @description Constructor used for the OSB_SendEmailPage (Visual force page)
     *
     * @param apex controller of contact type
     **/
    public OSB_SendEmailPage_CTRL(ApexPages.StandardController controller) {
        contactRecord =  (Contact) controller.getRecord();
        recordID = contactRecord.Id;
    }
    
    /**
     * @description Method is used to send AP invite Email
     *
     * @return PageReference to current record
     **/
    public PageReference sendApEmailInvite(){
        try{
            updateContact(contactRecord.Id);
            Map<String, Object> inputs = new Map<String, Object>();
            inputs.put('contactRecordID', contactRecord.Id);
            inputs.put('emailTemplateName', OSB_AP_CONTACT_ONEHUB_ADMIN_INVITE_EMAIL_TEMPLATE);
    
            if (!Test.isRunningTest()) {
                Flow.Interview.OSB_Contact_Onboarding_Email_Alerts myFlow = new Flow.Interview.OSB_Contact_Onboarding_Email_Alerts(inputs);
                myFlow.start();
            }

        }catch(Exception e){
            LOGGER.error('OSB_SendEmailPage_CTRL : updateContact Exception logged: ',e);
        }

        return new PageReference('/' + recordID);
    }
    
    /**
     * @description Used to update the the contact record after the invite email has been sent
     *
     * @param Contact ID of contact to update
     **/
    private static void updateContact(Id contactId){
        fflib_ISObjectUnitOfWork uow = ORG_Application.unitOfWork.newInstance();
		Contact contact = SEL_Contacts.newInstance().selectById(new Set<Id>{contactId})[0];
        User AdminUser = UTL_User.getSfAdministrationUser();
        contact.OSB_Community_Access_Status__c = DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_INVITE_SENT;
        contact.OSB_Date_Invite_Sent__c = system.now();
        if(String.isEmpty(contact.Onehub_Community_URL__c)){
            contact.Onehub_Community_URL__c = OSB_URLs__c.getValues(OSB_CUSTOM_SETTING_OSB_BASE_URL).Value__c + 's/sign-up?record=' + OSB_SRV_EncryptionHelper.encryptString(contact.Id, OSB_MAIL_ENCRKEY);
        }
        if(contact.RecordTypeId == UTL_RecordType.getRecordTypeId(DMN_Contact.OBJ_NAME, DMN_Contact.RTD_BANK)){
           contact.OSB_IsCommunityUser__c = true;
           contact.ownerId =AdminUser.Id; 
        }
        uow.registerDirty(contact);
		uow.commitWork();
    }

    /**
     * @description Used to hash the user email address 
     * @param email of the user 
     **/
    @AuraEnabled(cacheable=true)
    public static String encryptEmail(String email) {
        
        fflib_ISObjectUnitOfWork uow = ORG_Application.unitOfWork.newInstance();
        List<Contact> contacts = SEL_Contacts.newInstance().selectByEmail(new Set<String> {email});
    
        if (contacts.isEmpty()) {
            throw new IllegalArgumentException('No contact found with the provided email');
        }
    
        Contact contact = contacts[0];
        contact.Email = email.toLowerCase();
        Blob emailBlob = Blob.valueOf(email);
        Blob hash = Crypto.generateDigest('SHA-256', emailBlob);
        return EncodingUtil.convertToHex(hash);
    }
}


