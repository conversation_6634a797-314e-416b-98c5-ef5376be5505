/**
 * @description smartnudge user subscription controller class
 *
 * <AUTHOR> (<EMAIL>)
 * @UserStory SFP-25116
 * @date May 2023
 */
public without sharing class OSB_SN_UserSubscription_CTRL {
    private static final sbgplatform.rflib_Logger LOGGER = sbgplatform.rflib_LoggerUtil.getFactory()
        .createLogger('OSB_SN_UserSubscription_CTRL');
    /**
     * @description get onboarded user subscription preferences to Solution
     * @param solution subscribed solution name
     * @return Subscribed_Solutions__c user preferences
     */
    @AuraEnabled
    public static Subscribed_Solutions__c getUserPreferences(String solution) {
        LOGGER.info('OSB_SN_UserSubscription_CTRL:getUserPreferences initiated');
        String userEmail = Userinfo.getUserEmail();
        List<Subscribed_Solutions__c> preferences = SEL_SubscribedSolutions.newInstance()
            .selectByUserEmailAndSolutionTitles(
                new Set<String>{ userEmail },
                new Set<String>{ solution }
            );
        return preferences[0];
    }
    /**
    * @description get user subscription to Solution
    * @param solution subscribed solution name
    * @return boolean determining if user is subscribed or not
    */
    @AuraEnabled
    public static Boolean getUserSubscription(String solution) {
        LOGGER.info('OSB_SN_UserSubscription_CTRL:getUserSubscription initiated');
        Boolean isSubscribed = false;
        String subscription;
        try {
            Subscribed_Solutions__c solutionRecord = getUserPreferences(
                solution
            );
            if (solutionRecord != null) {
                subscription = solutionRecord.Subscribed__c;
                if (subscription == 'Yes') {
                    isSubscribed = true;
                }
            }
        } catch (Exception e) {
            LOGGER.error('OSB_SN_UserSubscription_CTRL : saveUserPreferences Exception logged: ',e);
        }
        return isSubscribed;
    }

    /**
     * @description updates user subscription preference to smartnudges
     *
     * @param subscribed subscription preference
     * @param emailAlerts email alerts preferences
     * @param emailFrequency email frequency preference
     * @param solution subscribed solution name
     * @return a success or an error message
     */
    @AuraEnabled
    public static String saveUserPreferences(
        String subscribed,
        String emailAlerts,
        String emailFrequency,
        String solution
    ) {
        LOGGER.info('OSB_SN_UserSubscription_CTRL:saveUserPreferences initiated');
        fflib_ISObjectUnitOfWork uow = ORG_Application.unitOfWork.newInstance();
        String response = 'Error';
        try {
            Subscribed_Solutions__c solutionRecord = getUserPreferences(
                solution
            );
            if (solutionRecord != null) {
                solutionRecord.Subscribed__c = subscribed;
                solutionRecord.Email_Alerts__c = emailAlerts;
                solutionRecord.Email_Frequency__c = emailFrequency;
                uow.registerDirty(solutionRecord);
                uow.commitWork();
                response = 'Success';
            }
        } catch (Exception e) {
            LOGGER.error('OSB_SN_UserSubscription_CTRL : saveUserPreferences Exception logged: ',e);
        }
        return response;
    }
}