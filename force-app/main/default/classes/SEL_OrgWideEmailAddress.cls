/**
 * @description       : US: SFP-11298 Add to calendar from email functionality
 * <AUTHOR> TCK
 * @group             : 
 * @last modified on  : 06-14-2022
 * @last modified by  : TCK
**/
public without sharing class SEL_OrgWideEmailAddress extends fflib_SObjectSelector {

    /**
    * @description 
    * <AUTHOR> | 06-14-2022 
    * @return List<Schema.SObjectField> 
    **/
    public List<Schema.SObjectField> getSObjectFieldList() {

        return new List<Schema.SObjectField> {
            OrgWideEmailAddress.Id
        };
    }

    /**
    * @description 
    * <AUTHOR> | 06-14-2022 
    * @return Schema.SObjectType 
    **/
    public Schema.SObjectType getSObjectType() {
        return OrgWideEmailAddress.sObjectType;
    }

    /**
    * @description 
    * <AUTHOR> | 06-14-2022 
    * @return SEL_OrgWideEmailAddress 
    **/
    public static SEL_OrgWideEmailAddress newInstance() {
        return(SEL_OrgWideEmailAddress) ORG_Application.selector.newInstance(OrgWideEmailAddress.SObjectType);
    }

    /**
    * @description 
    * <AUTHOR> | 06-14-2022 
    * @param address 
    * @return List<OrgWideEmailAddress> 
    **/
    public List<OrgWideEmailAddress> selectByAddress(String address) {
        return (List<OrgWideEmailAddress>) Database.query(
                        newQueryFactory()
                        .setCondition('Address =: address')
                        .toSOQL());
    }
}