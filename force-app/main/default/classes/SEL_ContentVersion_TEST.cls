/**
 * @description       : 
 * <AUTHOR> TCK
 * @group             : 
 * @last modified on  : 06-15-2022
 * @last modified by  : TCK
**/
@IsTest
private class SEL_ContentVersion_TEST  {
    @IsTest
    static void shouldSelectByContentDocumentId() {
        Test.startTest();
        SEL_ContentVersion.newInstance().selectByContentDocumentId(new Set<Id>());
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        System.assert(result.getCondition().containsIgnoreCase('ContentDocumentId IN: contentDocumentIds'), 'Is not doing the right condition');
    }
}