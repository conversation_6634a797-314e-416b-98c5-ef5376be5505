/***
* <AUTHOR> <PERSON><PERSON><PERSON>(<EMAIL>)
* @date    		: 31 OCT 2023
* @description 	: SFP-31190 - Selector layer for the object Insight_Action__c
**/
public with sharing class SEL_InsightActions extends fflib_SObjectSelector{
    /**
    * @description getSObjectFieldList
    * @return List<Schema.SObjectField>
    */
    public List<Schema.SObjectField> getSObjectFieldList() {
        return new List<Schema.SObjectField> {
            	Insight_Action__c.Id,
                Insight_Action__c.CreatedDate,    
                Insight_Action__c.Action_Type__c, 
                Insight_Action__c.Category__c,
                Insight_Action__c.Contact__c, 
                Insight_Action__c.owner.id,
                Insight_Action__c.Insight__c,
                Insight_Action__c.Insight_Quality__c, 
                Insight_Action__c.Insight_Status__c,
                Insight_Action__c.Is_Snooze_Value__c, 
                Insight_Action__c.Reason__c, 
                Insight_Action__c.Sub_Category__<PERSON>, 
                Insight_Action__c.User__c,
                Insight_Action__c.Role__c   
                };
    }
    
    /**
    * @description selectById
    * @return Schema.SObjectType
    */
    public Schema.SobjectType getSObjectType(){
        return Insight_Action__c.SobjectType;
    }
    
    /**
    * @description selectInsightActionsByInsightIds
    * @param insightIds 
    * @param actionTypes 
    * @return List<Insight_Action__c>
    */
    public List<Insight_Action__c> selectByInsightIdsAndActionTypes(Set<String> insightIds,List<String> actionTypes) {
        String whereCondition = 'Insight__c IN : insightIds';
        if(!actionTypes.isEmpty()){
            whereCondition = whereCondition + ' AND Action_Type__c IN : actionTypes';
        }
        return (List<Insight_Action__c>) Database.query(
            newQueryFactory().
            setCondition(whereCondition).
            selectField('User__r.Name').
            selectField('Contact__r.Name').
            selectField('Contact__r.OSB_Community_Access_Role__c').
            selectField('Insight__r.OwnerId').
            selectField('Insight__r.Is_Snoozed__c').
            toSOQL());
    }
    
    /**
    * @description Select without condition
    * @return List<Insight_Action__c>
    */
    public List<Insight_Action__c> selectWithoutCondition() {
        return (List<Insight_Action__c>) Database.query(
            newQueryFactory().toSOQL()
        );
    }

}