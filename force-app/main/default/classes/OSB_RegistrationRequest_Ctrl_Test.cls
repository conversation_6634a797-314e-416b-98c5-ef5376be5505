/**
 * Test class for the OSB_RedirectReg_Ctrl class
 *
 * <AUTHOR> (Wayde.<PERSON><EMAIL>)
 * @date        April 2020
 * 
 * <AUTHOR>
 * @date Last Modified  April 2023
 */
@IsTest
public class OSB_RegistrationRequest_Ctrl_Test {
    private static String TEST_USER_NAME = '<EMAIL>';

    @IsTest
    static void testSaveCase() {
        Case regCase = (Case) new BLD_Case()
            .setOSBData()
            .suppliedEmail(TEST_USER_NAME)
            .getRecord();
        Case result = OSB_RegistrationRequest_Ctrl.saveCase(regCase);
        Test.stopTest();
        Assert.areEqual('<EMAIL>', testUserName, 'Success');
    }

    @IsTest
    static void testDecodeBase64String() {
        String base64String = 'dHJ1ZXx0cnVlfGZhbHNl';
        Test.startTest();
        base64String = OSB_RegistrationRequest_Ctrl.decodeBase64String(base64String);
        Test.stopTest();        
        Assert.areEqual('true|true|false',base64String,'true');
    }

    @IsTest
    static void negativeTestDecodeBase64String() {
        String base64String = null;
        Test.startTest();
        base64String = OSB_RegistrationRequest_Ctrl.decodeBase64String(
            base64String
        );
        Test.stopTest();
        Assert.areEqual(null, base64String, 'null');
    }
    
    @IsTest
    static void testcheckDuplicate(){
    Test.startTest();

    Contact testDuplicate = new Contact();
    testDuplicate.Firstname = firstName;
    testDuplicate.lastname = lastName;
    testDuplicate.email = emailAddress;            
    Boolean isDuplicate =  OSB_RegistrationRequest_Ctrl.checkDuplicate(testDuplicate);
    Test.stopTest();
    Assert.areEqual(false, isDuplicate, 'The checkForDuplicate method did not identify the existing contact.');
    }
}
