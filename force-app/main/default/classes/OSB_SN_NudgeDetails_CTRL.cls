/**
 * @description This class used to load nudge details on contact dealer email template and download nudge details
 * <AUTHOR> 
 * @UserStory SFP-25123
 * @CreatedDate JULY 2023
 */
public without sharing class OSB_SN_NudgeDetails_CTRL {
	private static final sbgplatform.rflib_Logger LOGGER = sbgplatform.rflib_LoggerUtil.getFactory().createLogger('OSB_SN_NudgeDetails_CTRL');
	/*****************************
    * @description : nextSevenDayDate
    * ***************************/
    public String expireOnCss {get;set;}
    
    public Id nudgeRecId {get;set;}
    public String userName {get;set;}
    public String insightLink {get;set;}
    public String smartNudgeUrl {get;set;}
    public OSB_SN_NudgeDetails_CTRL(){
        userName = UserInfo.getName();
        smartNudgeUrl = Url.getOrgDomainURL().toExternalForm() + '/lightning/n/Akili_Insights?c__currentPage=1&c__currentTab=All&c__isMyClientNudgesSelected=true';
        LOGGER.debug('OSB_SN_NudgeDetails_CTRL: SmartNudge url :'+smartNudgeUrl);
    }
    
    /*****************************
    * @description : To return link for Header image
    * @return     	: String
    * ***************************/
    public String getHeaderImageURL(){
        return getImageLink('OSB_SN_Email_Banner_Image');
    }
    /*****************************
    * @description : To return link for SmartNudge Summary image
    * @return     	: String
    * ***************************/
    public String getSmartNudgeSummaryImageURL(){
        return getImageLink('OSB_SN_SmartNudge_Summary_Image');
    }
    
    /*****************************
    * @description To form url to show images on the notification template based on the documentId
    * @param developerName
    * @return String
    * **************************/
    public string getImageLink(String developerName){
        String urlPrefix = '/servlet/servlet.ImageServer?id=';
        String documentLink;
        List<Document> documents = SEL_Documents.newInstance().selectByDeveloperName(new Set<String>{developerName});
        if(!documents.isEmpty()) {
            documentLink =  Url.getSalesforceBaseUrl().toExternalForm() + urlPrefix + documents[0].Id + '&oid=' + UserInfo.getOrganizationId();
        }
        LOGGER.debug('OSB_SN_NudgeDetails_CTRL:getImageLink documentLink url :'+smartNudgeUrl);
        return documentLink;
    }
    
    /*****************************
    * @description To fetch and return insight record
    * @return Insight__c
    * **************************/
    public Insight__c getNudge(){
        LOGGER.info('OSB_SN_NudgeDetails_CTRL:getNudge initiated'); 
        String recId = nudgeRecId;
        if(!String.isNotBlank(recId)){
            recId = ApexPages.CurrentPage().getparameters().get('id');
        }
        List<Insight__c> nudgeList = new SEL_Insights().getInsightsByIds(new Set<String>{recId});
        insightLink = Url.getOrgDomainURL().toExternalForm() + '/lightning/n/Akili_Insights?c__id='+nudgeList[0].Id; 
        Date twoDays = System.today().addDays(2);
        Date sevenDays = System.today().addDays(7);
        if(nudgeList[0].Expiry_Date__c <= twoDays){
            expireOnCss = 'color: #DC0A0A';
        }
        else if(nudgeList[0].Expiry_Date__c > twoDays && nudgeList[0].Expiry_Date__c <= sevenDays){
            expireOnCss = 'color:#FF681D';
        }
        else{
            expireOnCss = 'color:black';
        }
        LOGGER.debug('OSB_SN_NudgeDetails_CTRL:getNudge insight record url :'+insightLink);
        return nudgeList[0];        
    }    
}