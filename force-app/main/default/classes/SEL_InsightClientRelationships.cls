/********************************************
* <AUTHOR> <PERSON><PERSON><PERSON>(<EMAIL>)
* @date    		: 25 SEPT 2023
* @description 	: SFP-28741 - Selector layer for the object Insight_Client_Relationship__c
*
* @LastModified Nov 2023
* <AUTHOR> (<EMAIL>)
* @UserStory SFP-25120
* @LastModifiedReason Added field Is_Unread__c in the getSObjectFieldList and added new methods selectByContactIdAndIsUnread, selectByContactIdAndIsSnoozed, selectByContactIdAndInsightId and selectByContactId 
**/
public inherited sharing class SEL_InsightClientRelationships extends fflib_SObjectSelector{
    /**
    * @description getSObjectFieldList
    * @return List<Schema.SObjectField>
    */
    public List<Schema.SObjectField> getSObjectFieldList() {
        return new List<Schema.SObjectField> {
            	Insight_Client_Relationship__c.Id,
                Insight_Client_Relationship__c.Name, 
                Insight_Client_Relationship__c.Contact__c,
                Insight_Client_Relationship__c.Insight__c, 
                Insight_Client_Relationship__c.owner.Id,
                Insight_Client_Relationship__c.Is_Unread__c
                };
    }
    
    /**
    * @description selectById
    * @return Schema.SObjectType
    */
    public Schema.SobjectType getSObjectType(){
        return Insight_Client_Relationship__c.SobjectType;
    }
    
    /**
    * @description selectActiveInsightClientRelationships
    * @param contactIds 
    * @param numberOfDays
    * @param limitCount
    * @return List<Insight_Client_Relationship__c>
    */
    public List<Insight_Client_Relationship__c> selectActiveInsightClientRelationships(Set<Id> contactIds,Integer numberOfDays, Integer limitCount) {
        String whereCondition = 'Insight__r.Persona__c = \'Client\' AND Insight__r.Event_Date__c = LAST_N_DAYS:'+numberOfDays+' AND Insight__r.Opportunity__c = null AND Insight__r.Is_Expired__c=false AND Insight__r.Is_Snoozed__c = false AND Insight__r.Is_Provided_Feedback__c=false';
        if(!contactIds.isEmpty()){
            whereCondition = whereCondition + ' AND Contact__c IN : contactIds';
        }
        return (List<Insight_Client_Relationship__c>) Database.query(
            newQueryFactory().setCondition(whereCondition).setLimit(limitCount).toSOQL());
    }
    
    /**
    * @description selectByContactIdAndIsUnread
    * @param contactIds 
    * @param isUnread
    * @return List<Insight_Client_Relationship__c>
    */
    public List<Insight_Client_Relationship__c> selectByContactIdAndIsUnread(Set<String> contactIds,Boolean isUnread) {
        String whereCondition = 'Is_Unread__c =:isUnread';
        whereCondition = whereCondition + ' AND Insight__r.Persona__c = \'Client\' AND Insight__r.Opportunity__c = null AND Insight__r.Is_Expired__c=false AND Insight__r.Is_Provided_Feedback__c=false';
        if(!contactIds.isEmpty()){
            whereCondition = whereCondition + ' AND Contact__c IN : contactIds';
        }
        return (List<Insight_Client_Relationship__c>) Database.query(
            newQueryFactory().setCondition(whereCondition).toSOQL());
    }
    /**
    * @description selectByContactIdAndIsSnoozed
    * @param contactIds 
    * @param isSnoozed
    * @return List<Insight_Client_Relationship__c>
    */
    public List<Insight_Client_Relationship__c> selectByContactIdAndIsSnoozed(Set<String> contactIds,Boolean isSnoozed) {
        String whereCondition = 'Insight__r.Is_Snoozed__c =:isSnoozed';
        whereCondition = whereCondition + ' AND Insight__r.Persona__c = \'Client\' AND Insight__r.Opportunity__c = null AND Insight__r.Is_Expired__c=false AND Insight__r.Is_Provided_Feedback__c=false';
        if(!contactIds.isEmpty()){
            whereCondition = whereCondition + ' AND Contact__c IN : contactIds';
        }
        return (List<Insight_Client_Relationship__c>) Database.query(
            newQueryFactory().setCondition(whereCondition).toSOQL());
    }
    /**
    * @description selectByContactId
    * @param contactIds 
    * @return List<Insight_Client_Relationship__c>
    */
    public List<Insight_Client_Relationship__c> selectByContactId(Set<String> contactIds) {
        String whereCondition = 'Insight__r.Persona__c = \'Client\' AND Insight__r.Opportunity__c = null AND Insight__r.Is_Expired__c=false AND Insight__r.Is_Provided_Feedback__c=false';
        whereCondition = whereCondition + ' AND Contact__c IN : contactIds';
        return (List<Insight_Client_Relationship__c>) Database.query(
            newQueryFactory().setCondition(whereCondition).toSOQL());
    }
    /**
    * @description selectByContactIdAndInsightId
    * @param contactIds 
    * @param insightIds 
    * @return List<Insight_Client_Relationship__c>
    */
    public List<Insight_Client_Relationship__c> selectByContactIdAndInsightId(Set<String> contactIds,Set<String> insightIds) {
        String whereCondition = 'Contact__c IN : contactIds AND Insight__c IN : insightIds';
        return (List<Insight_Client_Relationship__c>) Database.query(
            newQueryFactory().setCondition(whereCondition).toSOQL());
    }
    /**
     * @description Calls method selectByContactIdAndInsightId in without sharing context,'WoSharing' stands for 'Without Sharing'
     * @param contactIds
     * @param insightIds
     * @return list of contacts
     */
    public List<Insight_Client_Relationship__c> selectByContactIdAndInsightIdWoSharing(Set<String> contactIds,Set<String> insightIds) {
        return new WithoutSharing().selectByContactIdAndInsightId(this,contactIds,insightIds);
    }
    /**
     * @description Calls method selectByContactId in without sharing context,'WoSharing' stands for 'Without Sharing'
     * @param contactIds
     * @return list of contacts
     */
    public List<Insight_Client_Relationship__c> selectByContactIdWoSharing(Set<String> contactIds) {
        return new WithoutSharing().selectByContactId(this,contactIds);
    }
    /**
     * @description Calls method selectByContactIdAndIsSnoozed in without sharing context,'WoSharing' stands for 'Without Sharing'
     * @param contactIds
     * @param isSnoozed
     * @return list of contacts
     */
    public List<Insight_Client_Relationship__c> selectByContactIdAndIsSnoozedWoSharing(Set<String> contactIds,Boolean isSnoozed) {
        return new WithoutSharing().selectByContactIdAndIsSnoozed(this,contactIds,isSnoozed);
    }
    /**
     * @description Calls method selectByContactIdAndIsUnread in without sharing context,'WoSharing' stands for 'Without Sharing'
     * @param contactIds
     * @param isUnread
     * @return List<Insight_Client_Relationship__c>
     */
    public List<Insight_Client_Relationship__c> selectByContactIdAndIsUnreadWoSharing(Set<String> contactIds,Boolean isUnread) {
        return new WithoutSharing().selectByContactIdAndIsUnread(this,contactIds,isUnread);
    }
    /**
     * @description Calls method selectByContactIdAndIsUnread in without sharing context,'WoSharing' stands for 'Without Sharing'
     */
    /**
    * @description Select without condition
    * @return List<Insight_Client_Relationship__c>
    */
    public List<Insight_Client_Relationship__c> selectWithoutCondition() {
        return (List<Insight_Client_Relationship__c>) Database.query(
            newQueryFactory().toSOQL()
        );
    }

    /**
     * @description Is used for omitting sharing setting, when needed
     */
    private without sharing class WithoutSharing {
        /**
         * @description Returns list of Insight Client Relationships by id without sharing
         * @param selContact SEL_InsightClientRelationships InsightClientRelationship selector instance
         * @param contactIds
         * @param isUnread
         * @return List<Insight_Client_Relationship__c>
         */
        public List<Insight_Client_Relationship__c> selectByContactIdAndIsUnread(SEL_InsightClientRelationships selInsightClientRelationship, Set<String> contactIds,Boolean isUnread) {
            return selInsightClientRelationship.selectByContactIdAndIsUnread(contactIds, isUnread);
        }
        /**
         * @description Returns list of Insight Client Relationships by id without sharing
         * @param selContact SEL_InsightClientRelationships InsightClientRelationship selector instance
         * @param contactIds
         * @param isSnoozed
         * @return List<Insight_Client_Relationship__c>
         */
        public List<Insight_Client_Relationship__c> selectByContactIdAndIsSnoozed(SEL_InsightClientRelationships selInsightClientRelationship, Set<String> contactIds,Boolean isSnoozed) {
            return selInsightClientRelationship.selectByContactIdAndIsSnoozed(contactIds, isSnoozed);
        }
        /**
         * @description Returns list of Insight Client Relationships by id without sharing
         * @param selContact SEL_InsightClientRelationships InsightClientRelationship selector instance
         * @param contactIds
         * @return List<Insight_Client_Relationship__c>
         */
        public List<Insight_Client_Relationship__c> selectByContactId(SEL_InsightClientRelationships selInsightClientRelationship, Set<String> contactIds) {
            return selInsightClientRelationship.selectByContactId(contactIds);
        }
        
        /**
         * @description Returns list of Insight Client Relationships by id without sharing
         * @param selContact SEL_InsightClientRelationships InsightClientRelationship selector instance
         * @param contactIds
         * @param insightIds
         * @return List<Insight_Client_Relationship__c>
         */
        public List<Insight_Client_Relationship__c> selectByContactIdAndInsightId(SEL_InsightClientRelationships selInsightClientRelationship, Set<String> contactIds,Set<String> insightIds) {
            return selInsightClientRelationship.selectByContactIdAndInsightId(contactIds,insightIds);
        }
    }
}