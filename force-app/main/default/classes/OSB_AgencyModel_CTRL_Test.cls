/**
 * @description Test class for OSB_AgencyModel_CTRL
 *
 * <AUTHOR> (<EMAIL>)
 * @date September 2024
 */
@isTest
public class OSB_AgencyModel_CTRL_Test {
    private static AuthorizationForm TEST_AUTHORIZATION_FORM_PREVIOUS;
    private static AuthorizationForm TEST_AUTHORIZATION_FORM_CURRENT;
    private static AuthorizationFormText TEST_AUTHORIZATION_FORM_TEXT;
    private static Contact TEST_CONTACT;
    private static Knowledge__kav TEST_ARTICLE;
    private static AuthorizationFormConsent TEST_CONSENT;
    private static final String FORM_TEXT = 'Test details of the form';
    private static final String EXCEPTION_TEXT = 'Script-thrown exception';

    @testSetup
    static void setupTestData() {
        AuthorizationForm authFormPrevious = new AuthorizationForm(
            Name = 'OneHub | Agency Disclaimer',
            RevisionNumber = '1'
        );
        insert authFormPrevious;

        AuthorizationForm authFormCurrent = new AuthorizationForm(
            Name = 'OneHub | Agency Disclaimer',
            RevisionNumber = '2'
        );
        insert authFormCurrent;

        AuthorizationFormText authFormtext = new AuthorizationFormText(
            AuthorizationFormId = authFormCurrent.Id,
            Name = 'Test Authorization Form Text 2',
            DetailAuthorizationFormText = FORM_TEXT,
            IsActive = true,
            Locale = 'en_ZA',
            TextVersion__c = 2
        );
        insert authFormtext;

        authFormCurrent.DefaultAuthFormTextId = authFormtext.Id;
        update authFormCurrent;

        Contact contact = new Contact(LastName = 'Tester');
        insert contact;

        Knowledge__kav article = new Knowledge__kav(
            Title = 'Tester Name',
            UrlName = 'Testname'
        );
        insert article;

        AuthorizationFormConsent cons = new AuthorizationFormConsent(
            AuthorizationFormTextId = authFormtext.Id,
            ConsentCapturedDateTime = System.now(),
            ConsentCapturedSource = 'Test Source',
            ConsentCapturedSourceType = 'Web',
            ConsentGiverId = contact.Id,
            Knowledge__c = article.Id,
            Name = 'Test Consent',
            Status = 'Signed'
        );
        insert cons;
    }

    static void assignVariables() {
        TEST_CONTACT = [SELECT Id, Name FROM Contact LIMIT 1];
        TEST_CONSENT = [SELECT Id, Name, AuthorizationFormTextId, CreatedDate, ConsentGiverId, Knowledge__c, ConsentExpirationDateTime FROM AuthorizationFormConsent LIMIT 1];
        TEST_AUTHORIZATION_FORM_PREVIOUS = [SELECT Id, Name FROM AuthorizationForm WHERE RevisionNumber = '1' LIMIT 1];
        TEST_AUTHORIZATION_FORM_CURRENT = [SELECT Id, Name FROM AuthorizationForm WHERE RevisionNumber = '2' LIMIT 1];
        TEST_AUTHORIZATION_FORM_TEXT = [SELECT Id, DetailAuthorizationFormText FROM AuthorizationFormText WHERE AuthorizationFormId = :TEST_AUTHORIZATION_FORM_CURRENT.Id LIMIT 1];
        TEST_ARTICLE = [SELECT Id, Title FROM Knowledge__kav LIMIT 1];
    }

    @IsTest
    static void testGetAuthorizationFormText() {
        Test.startTest();
        AuthorizationFormText result = OSB_AgencyModel_CTRL.getAuthorizationFormText();
        Test.stopTest();
        
        Assert.areEqual(FORM_TEXT, result.DetailAuthorizationFormText, 'The correct AuthorizationFormText retrieved');
    }

    @isTest
    static void testGetConsentStatusForContact() {
        assignVariables();

        fflib_ApexMocks mocks = new fflib_ApexMocks();
    
        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(SEL_Contacts.class);
        SEL_AuthorizationFormConsent consentsSel = (SEL_AuthorizationFormConsent) mocks.mock(SEL_AuthorizationFormConsent.class);

        mocks.startStubbing();
        mocks.when(contactsSel.sObjectType()).thenReturn(Contact.SObjectType);
        mocks.when(contactsSel.selectByUserId((Set<ID>) fflib_Match.anyObject())).thenReturn(new List<Contact> {TEST_CONTACT});
        mocks.when(consentsSel.sObjectType()).thenReturn(AuthorizationFormConsent.SObjectType);
        mocks.when(consentsSel.selectByContactId((Set<Id>) fflib_Match.anyObject()))
            .thenReturn(new List<AuthorizationFormConsent>{ TEST_CONSENT });
        mocks.stopStubbing();
    
        ORG_Application.selector.setMock(contactsSel);
        ORG_Application.selector.setMock(consentsSel);
    
        Test.startTest();
        Map<Id, Map<String, Object>> consentStatusMap = OSB_AgencyModel_CTRL.getLatestConsentPerContact();
        Test.stopTest();
    
        Map<String, Object> consentInfo = consentStatusMap.get(TEST_CONSENT.Id);
        Assert.areEqual(TEST_ARTICLE.id, consentInfo.get('knowledgeId'), 'AuthorizationFormConsent Retrieved with correct knowledge details');
        Assert.areEqual(true, consentInfo.get('isLatestConsent'), 'AuthorizationFormConsent is the latest');
    }

    @isTest
    static void testGetConsentStatusForContactException() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
    
        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(SEL_Contacts.class);
        SEL_AuthorizationFormConsent consentsSel = (SEL_AuthorizationFormConsent) mocks.mock(SEL_AuthorizationFormConsent.class);
        
        Exception exceptionToThrow = new HandledException('Failed to retrieve consent');
        mocks.startStubbing();
        mocks.when(contactsSel.sObjectType()).thenReturn(Contact.SObjectType);
        mocks.when(contactsSel.selectByUserId((Set<ID>) fflib_Match.anyObject())).thenReturn(new List<Contact> {TEST_CONTACT});
        mocks.when(consentsSel.sObjectType()).thenReturn(AuthorizationFormConsent.SObjectType);
        mocks.when(consentsSel.selectByContactId((Set<Id>) fflib_Match.anyObject()))
            .thenThrow(exceptionToThrow);
        mocks.stopStubbing();
    
        ORG_Application.selector.setMock(contactsSel);
        ORG_Application.selector.setMock(consentsSel);
    
        Test.startTest();
        try { 
            Map<Id, Map<String, Object>> consentStatusMap = OSB_AgencyModel_CTRL.getLatestConsentPerContact();
        } catch(Exception e) {
            Assert.areEqual(EXCEPTION_TEXT, e.getMessage(),'Retrieved Consents per Contact and their status Exception Thrown');
        }
        Test.stopTest(); 
    }

    @IsTest
    static void testUpdateConsentEndDate() {
        Test.startTest();
        assignVariables();
        String updatedConsent = OSB_AgencyModel_CTRL.updateConsentEndDate(TEST_CONSENT.Id); 
        Test.stopTest();

        Assert.areEqual(true, String.isNotEmpty(updatedConsent), 'Consent Record updated by populating ExpirationDateTime');
    }

    @IsTest
    static void testUpdateConsentEndDateExceptions() {
        Test.startTest();
        assignVariables();
        try{ 
            String updatedConsent = OSB_AgencyModel_CTRL.updateConsentEndDate(TEST_CONTACT.Id);
        } catch(Exception e) {
            Assert.areEqual(EXCEPTION_TEXT,e.getMessage(),'Update Consent Exception Thrown');
        }
        Test.stopTest(); 
    }

    @IsTest
    static void testCreateConsentRecord() {
        assignVariables();
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        fflib_ISObjectUnitOfWork uowMock = new fflib_SObjectMocks.SObjectUnitOfWork(mocks);

        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(SEL_Contacts.class);
        ORG_Application.unitOfWork.setMock(uowMock);

        mocks.startStubbing();
        mocks.when(contactsSel.sObjectType()).thenReturn(Contact.SObjectType);
        mocks.when(contactsSel.selectByUserId((Set<ID>) fflib_Match.anyObject())).thenReturn(new List<Contact> {TEST_CONTACT});
        mocks.stopStubbing();

        ORG_Application.selector.setMock(contactsSel);

        Map<String, Object> consentDetails = new Map<String, Object>{
            'authorizationFormTextId' => TEST_AUTHORIZATION_FORM_TEXT.Id,
            'knowledgeId' => TEST_ARTICLE.Id,
            'knowledgeName' => TEST_ARTICLE.Title
        };

        Test.startTest();
        OSB_AgencyModel_CTRL.createConsent(consentDetails);
        Test.stopTest();

        fflib_ArgumentCaptor argument = fflib_ArgumentCaptor.forClass(fflib_ISObjectUnitOfWork.class);
        ((fflib_ISObjectUnitOfWork) mocks.verify(uowMock, 1)).registerNew((AuthorizationFormConsent) argument.capture());
        AuthorizationFormConsent createdConsent = (AuthorizationFormConsent) argument.getValue();
        
        Assert.areEqual(TEST_ARTICLE.id, createdConsent.Knowledge__c, 'Consent Record Created with correct knowledge details.');
        Assert.areEqual(TEST_CONTACT.id, createdConsent.ConsentGiverId, 'Consent Record Created with correct Contact details.');
        Assert.areEqual(consentDetails.get('authorizationFormTextId'), createdConsent.authorizationFormTextId, 'Consent Record Created with correct Text details.');
    }

    @isTest
    static void testCreateException() {
        assignVariables();
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        fflib_ISObjectUnitOfWork uowMock = new fflib_SObjectMocks.SObjectUnitOfWork(mocks);

        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(SEL_Contacts.class);
        ORG_Application.unitOfWork.setMock(uowMock);

        Exception exceptionToThrow = new HandledException('Failed to create consent');
        mocks.startStubbing();
        mocks.when(contactsSel.sObjectType()).thenReturn(Contact.SObjectType);
        mocks.when(contactsSel.selectByUserId((Set<Id>) fflib_Match.anyObject())).thenThrow(exceptionToThrow);
        mocks.stopStubbing();

        ORG_Application.selector.setMock(contactsSel);

        Map<String, Object> consentDetails = new Map<String, Object>{
            'authorizationFormTextId' => null,
            'knowledgeId' => TEST_ARTICLE.Id,
            'knowledgeName' => TEST_ARTICLE.Title
        };

        Test.startTest();
        try { 
            OSB_AgencyModel_CTRL.createConsent(consentDetails);
        } catch(Exception e) {
            Assert.areEqual(EXCEPTION_TEXT, e.getMessage(),'Create Consent Exception Thrown');
        } 
        Test.stopTest();
    }
}