/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @description : Test class for SmartNudge_InsightsInMyPortfolioHelper
 * @date 		: 12th August 2022
 */
@isTest
public class SmartNudge_InsightsInMyPortfolio_TEST {
    
    /**
     * @description :  This method will test for more than 10 insights scenario
     */
    @isTest
    static void moreThan10InsightFoundTest1(){
        
        List<Schema.SObjectType> MY_SOBJECTS = new Schema.SObjectType[]{Insight__c.SObjectType,Account.SObjectType};
        fflib_SObjectUnitOfWork uow1 = new fflib_SObjectUnitOfWork(MY_SOBJECTS);
        
        new BLD_Account(uow1).bpid('35').usePBB();
        new BLD_Account(uow1).bpid('37').usePBB();
         new BLD_Account(uow1).bpid('38').usePBB();
        new BLD_Account(uow1).bpid('36').usePBB();
         new BLD_Account(uow1).bpid('39').usePBB();
        new BLD_Account(uow1).bpid('40').usePBB();
         new BLD_Account(uow1).bpid('41').usePBB();
        new BLD_Account(uow1).bpid('42').usePBB();
         new BLD_Account(uow1).bpid('43').usePBB();
        new BLD_Account(uow1).bpid('44').usePBB();
         new BLD_Account(uow1).bpid('45').usePBB();
        new BLD_Account(uow1).bpid('46').usePBB();
         
         uow1.commitWork();
        
        List<Account> accList = new SEL_Accounts().selectWithoutCondition();
		Test.startTest();
         fflib_SObjectUnitOfWork uow = new fflib_SObjectUnitOfWork(MY_SOBJECTS);
		BLD_Insight insight = new BLD_Insight(uow).category('FX TIPS');
		insight.owner(UserInfo.getUserId());
        insight.snoozed(false);
        insight.eventDate(Date.today());
        //bAccount.client(accBld.getRecordId());
        insight.insight('Test Insight');
        insight.clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).client(accList[0].Id).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).client(accList[1].Id).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).client(accList[2].Id).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).client(accList[3].Id).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).client(accList[4].Id).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).client(accList[5].Id).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).client(accList[6].Id).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).client(accList[7].Id).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).client(accList[8].Id).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).client(accList[9].Id).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).client(accList[10].Id).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
        uow.commitWork();
        
        SmartNudge_InsightsInMyPortfolioHelper vfcController = new SmartNudge_InsightsInMyPortfolioHelper();
        vfcController.cstTeamMemberId = UserInfo.getUserId();
        vfcController.cstTeamMemberName = UserInfo.getName();
        vfcController.getFooterClickHereImageURL();
        vfcController.getHeaderImageURL();
        vfcController.getSmartNudgeUrl();
        vfcController.getMyLatestInsights();
        vfcController.getInsightDetails('Testing Insight Details field has more than 100 characters.Testing Insight Details field has more than 100 characters.');
        vfcController.getInsightDetails('Testing Insight Details field has less than 100 characters.');
       	
        Test.stopTest();
        System.assert(insight != null, 'Was expecting to find at least one insight');
        
    }
	/**
     * @description :  This method will test for more than 10 insights scenario
     */
    @isTest
    static void moreThan10InsightFoundTest(){
        Test.startTest();
        List<Schema.SObjectType> MY_SOBJECTS = new Schema.SObjectType[]{Insight__c.SObjectType};
        fflib_SObjectUnitOfWork uow = new fflib_SObjectUnitOfWork(MY_SOBJECTS);
        //BLD_Account accBld = (BLD_Account) new BLD_Account().useChild().commitWork();
        //fflib_SObjectUnitOfWork uow = ABS_ObjectBuilderBase.getNewUnitOfWork();

		BLD_Insight insight = new BLD_Insight(uow).category('FX TIPS');
		insight.owner(UserInfo.getUserId());
        insight.snoozed(false);
        insight.eventDate(Date.today());
        //bAccount.client(accBld.getRecordId());
        insight.insight('Test Insight');
        insight.clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
       	new BLD_Insight(uow).category('FX TIPS').owner(UserInfo.getUserId()).snoozed(false).eventDate(Date.today()).insight('Test Insight').clientCoordinator(UserInfo.getUserId());
        uow.commitWork();
        
        SmartNudge_InsightsInMyPortfolioHelper vfcController = new SmartNudge_InsightsInMyPortfolioHelper();
        vfcController.cstTeamMemberId = UserInfo.getUserId();
        vfcController.cstTeamMemberName = UserInfo.getName();
        vfcController.getFooterClickHereImageURL();
        vfcController.getHeaderImageURL();
        vfcController.getSmartNudgeUrl();
        vfcController.getMyLatestInsights();
        vfcController.getInsightDetails('Testing Insight Details field has more than 100 characters.Testing Insight Details field has more than 100 characters.');
        vfcController.getInsightDetails('Testing Insight Details field has less than 100 characters.');
       	
        Test.stopTest();
        System.assert(insight != null, 'Was expecting to find at least one insight');
        
    }
    /**
     * @description :  This method will test for less than 10 insights scenario
     */
    @isTest
    static void lessThan10InsightFoundTest(){
        Test.startTest();
        List<Schema.SObjectType> MY_SOBJECTS = new Schema.SObjectType[]{Insight__c.SObjectType};
        fflib_SObjectUnitOfWork uow = new fflib_SObjectUnitOfWork(MY_SOBJECTS);
        //BLD_Account accBld = (BLD_Account) new BLD_Account().useChild().commitWork();
        //fflib_SObjectUnitOfWork uow = ABS_ObjectBuilderBase.getNewUnitOfWork();

		BLD_Insight insight = new BLD_Insight(uow).category('FX TIPS');
		insight.owner(UserInfo.getUserId());
        insight.snoozed(false);
        insight.eventDate(Date.today());
        //bAccount.client(accBld.getRecordId());
        insight.insight('Test Insight');
        insight.clientCoordinator(UserInfo.getUserId());
        uow.commitWork();
        
        SmartNudge_InsightsInMyPortfolioHelper vfcController = new SmartNudge_InsightsInMyPortfolioHelper();
        vfcController.cstTeamMemberId = UserInfo.getUserId();
        vfcController.cstTeamMemberName = UserInfo.getName();
        vfcController.getMyLatestInsights();
        Test.stopTest();
        System.assert(insight != null, 'Was expecting to find at least one insight');
        
    }
    
    
    
    static List<Insight__c> createInsights( Integer numberOfInsightsToCreate){
        Account c=new Account();
        c.Name='Test insight with client name';
        insert c;
        List<Insight__c> insightList = new List<Insight__c>();
        for(Integer i = 0; i < numberOfInsightsToCreate; i++){
            Insight__c insight = new Insight__c();
            insight.Category__c = 'FX TIPS';
            insight.Is_Snoozed__c = false;
            insight.Event_Date__c=  Date.today();
            insight.Client__c= c.Id;
            insight.Insight__c = 'Insight Details ';
            insight.OwnerId = UserInfo.getUserId();
            insight.Client_Coordinator__c = UserInfo.getUserId();
            insightList.add(insight);
        }
        return insightList;

    }
}