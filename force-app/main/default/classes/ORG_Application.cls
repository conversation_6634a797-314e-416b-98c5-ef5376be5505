/**
 * @description Main application factory class which is responsible for returning the correct business implementation for the unit
 * of work, service, selector and domain layers within the application.
 *
 * NOTE: Please add values to selector and service in ALPHABETICAL order, to prevent duplicates
 * Values for unitOfWork have to to be in execution order
 *
 * <AUTHOR> <PERSON><PERSON><PERSON> (b<PERSON><PERSON><PERSON><PERSON>@deloittece.com)
 * @date                    : March 2020
 * @LastModifiedBy          : <PERSON>
 * @LastModifiedDate        : 19 July 2023
 *
 * @LastModified August 2023
 * <AUTHOR> (<EMAIL>)
 * @UserStory SFP-21026
 * @LastModifiedReason Addition of the OSB_SRV_SolutionAPI
 *
 * @LastModifiedBy          : <PERSON><PERSON>
 * @LastModifiedDate        : 14 September 2023
 * @ Modification Description : SFP-29983: Added support of the SEL class for the Agenda__c object
 * 
 * @LastModifiedBy          : Stane Murhula
 * @LastModifiedDate        : 04 April 2024
 * @ Modification Description : SFP-: Added support of the SEL class for the NBAC_Real_Estate_Finance__c object
 *
 * @LastModifiedBy          : <PERSON><PERSON>
 * @LastModifiedDate        : 12 December 2023
 * @ Modification Description : SFP-19834: Added  support for PP Partner application
 *
 * @LastModifiedBy          : Stane Murhula
 * @LastModifiedDate        : 23 July 2024
 * @ Modification Description : SFP-38151 Registered ECST Custom metadata types
 * 
 * @LastModifiedBy          : Nkosivile Mazima
 * @LastModifiedDate        : 15 Aug 2024
 * @ Modification Description : SFP-41271 Registered SEL_AutoEmailNotification
 *
 * @LastModifiedBy          : Mthobisi Ndlovu
 * @LastModifiedDate        : 23 Aug 2024
 * @ Modification Description : SFP-38288 - Registered SEL_Mall_AboutUsValueProposition
 * 
 * @LastModified October 2024
 * <AUTHOR> Milicevic (<EMAIL>)
 * @UserStory SFP-36706
 * @LastModifiedReason Addition of PermissionSetAssignment object for the uow and update of selector and service to alphabetical order
 * 
 * @LastModified October 2024
 * <AUTHOR> Keet (<EMAIL>)
 * @UserStory 
 * @LastModifiedReason Registered SEL_AuthorizationForm, SEL_AuthorizationFormText and SEL_AuthorizationFormConsent, and addition of objects for the uow
 */
public without sharing class ORG_Application {


    public static final fflib_Application.SelectorFactory selector = new fflib_Application.SelectorFactory(
        new Map<SObjectType, Type>{
            Account.SObjectType => SEL_Accounts.class,
            Account_Information__c.SObjectType => SEL_AccountInformations.class,
            AccountContactRelation.SObjectType => SEL_AccountContactRelation.class,
            Agenda__c.SObjectType => SEL_Agendas.class,
            AKILI_Insights_API__mdt.SObjectType => SEL_AkiliInsightApiMetadatas.class,
            AOB_Application__c.SObjectType => SEL_AOBApplication.class,
            AOB_DocumentType__mdt.SObjectType => SEL_DocumentType.class,
            AOB_SignaturePlacement__mdt.SObjectType => SEL_SignaturePlacement.class,
            Application_Document__c.SObjectType => SEL_ApplicationDocuments.class,
            Assessment_Opportunity__c.SObjectType => SEL_AssessmentOpportunities.class,
			AuthorizationForm.SObjectType => SEL_AuthorizationForm.class,
			AuthorizationFormText.SObjectType => SEL_AuthorizationFormText.class,
			AuthorizationFormConsent.SObjectType => SEL_AuthorizationFormConsent.class
			BCBP_Constants__mdt.SObjectType => SEL_BCBP_Constants.class,
            BCBP_Currency__mdt.SObjectType => SEL_BCBP_Currencies.class,
            BCBP_General_Enquiry__mdt.SobjectType => SEL_GeneralEnquiry.class,
            BCBP_Service_Request_Routing__mdt.SobjectType => SEL_BCBP_ServiceRequestRouting.class,
            Business_Assessment__c.SObjectType => SEL_BusinessAssessments.class,
            Call_Report__c.SObjectType => SEL_CallReports.class,
            Call_Report_Attendees__c.SObjectType => SEL_CallReportAttendees.class,
            Campaign.SObjectType => SEL_Campaign.class,
            Campaign_Hosts__c.SObjectType => SEL_CampaignHosts.class,
            Campaign_Member_Host__c.SObjectType => SEL_CampaignMemberHosts.class,
            CampaignMember.SObjectType => SEL_CampaignMember.class,
            Case.SObjectType => SEL_Cases.class,
            Child_products__c.SObjectType => SEL_ChildProducts.class,
            Client_Plan__c.SObjectType => SEL_ClientPlans.class,
            CMN_Account_Contact_Relationship_Detail__c.SObjectType => SEL_CMN_AccountContactRelationshipDetail.class,
            CMN_Account_Contact_Relationship_Detail__c.SObjectType => SEL_CMN_AccountContactRelationshipDetail.class,
            CMN_SupportTeam__c.SobjectType => SEL_SupportTeams.class,
            Consent_Configuration__c.SObjectType => SEL_ConsentConfiguration.class,
            Contact.SObjectType => SEL_Contacts.class,
            ContactPointTypeConsent.SObjectType => SEL_ContactPointTypeConsents.class,
            Content_Unit_Translation__c.SObjectType => SEL_ContentUnitTranslations.class,
            ContentDistribution.SObjectType => SEL_ContentDistribution.class,
            ContentDocument.SObjectType => SEL_ContentDocument.class,
            ContentDocumentLink.SObjectType => SEL_ContentDocumentLink.class,
            ContentNote.SObjectType => SEL_ContentNote.class,
            ContentVersion.SObjectType => SEL_ContentVersion.class,
            Conversation__c.SObjectType => SEL_Conversations.class,
            Conversation_Value__c.SObjectType => SEL_ConversationValues.class,
            Country_Information__mdt.SObjectType => SEL_BCBP_Countries.class,
            CurrencyType.SObjectType => SEL_CurrencyTypes.class,
            Custom_Client_Team__c.SObjectType => SEL_ClientTeams.class,
            CustomNotificationType.SObjectType => SEL_CustomNotificationTypes.class,
            CustomPermission.SObjectType => SEL_CustomPermissions.class,
            DataUsePurpose.SObjectType => SEL_DataUsePurposes.class,
            Document.SObjectType => SEL_Documents.class,
            Draw_Down_Profile__c.SObjectType => SEL_DrawdownProfiles.class,
            EAP_AppEvent__c.SObjectType => SEL_EAP_AppEvent.class,
            EAP_Attendance__c.sObjectType => SEL_EAP_Attendance.class,
            EAP_Attendee__c.sObjectType => SEL_EAP_Attendee.class,
            EAP_Document__c.sObjectType => SEL_EAP_Document.class,
            EAP_EventVenue__c.sObjectType => SEL_EAP_EventVenue.class,
            EAP_Meeting__c.sObjectType => SEL_EAP_Meeting.class,
            EAP_MeetingAvailability__c.sObjectType => SEL_EAP_MeetingAvailability.class,
            EAP_TravelInformation__c.sObjectType => SEL_EAP_TravelInformation.class,
            EAP_Venue__c.sObjectType => SEL_EAP_Venue.class,
            EmailMessage.SObjectType => SEL_EmailMessages.class,
            EmailTemplate.sObjectType => SEL_EmailTemplate.class,
            Entitlement.SObjectType => SEL_Entitlement.class,
            FeedItem.SObjectType => SEL_FeedItems.class,
            Financial_Account__c.SObjectType => SEL_FinancialAccounts.class,
            Financial_Account_Permission__c.SObjectType => SEL_FinancialAccountPermissions.class,
            FinServ__Revenue__c.SObjectType => SEL_Revenue.class,
            Global_Client_Revenue__c.SObjectType => SEL_GlobalClientRevenue.class,
            Group.SObjectType => SEL_Group.class,
            GroupMember.SObjectType => SEL_GroupMember.class,
            Individual.SObjectType => SEL_Individuals.class,
            Insight__c.SObjectType => SEL_Insights.class,
            Insight_Action__c.SObjectType => SEL_InsightActions.class,
            Insight_Client_Relationship__c.SObjectType => SEL_InsightClientRelationships.class,
            Investor_Participation__c.SObjectType => SEL_InvestorParticipations.class,
            Knowledge_Entitlement_Exception__c.SObjectType => SEL_KnowledgeExceptions.class,
            Link__c.SObjectType => SEL_Links.class,
            LiveChatTranscript.SObjectType => SEL_LiveChatTranscripts.class,
            LoginHistory.SObjectType => SEL_LoginHistories.class,
            Mall_About_Us_Value_Proposition__mdt.SObjectType => SEL_Mall_AboutUsValueProposition.class,
            Mall_Auth_Solution__mdt.SObjectType => SEL_Mall_AuthSolution.class,
            Mall_Dashboard_Widget__c.SObjectType => SEL_MallDashboardWidgets.class,
            Mall_Sub_Navigation_Item__mdt.SObjectType => SEL_Mall_SubNavigationItem.class,
            MallServiceRequestRouting__mdt.SObjectType => SEL_MallServiceRequestRouting.class,
            NBAC_Real_Estate_Finance__c.SObjectType => SEL_NbacRealEstateFinance.class,
            Notification__c.SObjectType => SEL_Notifications.class,
            Offering__c.SObjectType => SEL_Offerings.class,
            Onboarding_Application__c.SObjectType => SEL_OnboardingApplications.class,
            Opportunity.SObjectType => SEL_Opportunities.class,
            OpportunityTeamMember.SObjectType => SEL_OpportunityTeamMembers.class,
            Option_List_Item__c.SObjectType => SEL_Option_List_Item.class,
            OrgWideEmailAddress.SObjectType => SEL_OrgWideEmailAddress.class,
            OSB_AdobeAnalyticsData__mdt.SObjectType => SEL_AdobeAnalyticsData.class,
            OTPRequest__c.SObjectType => SEL_OTPRequest.class,
            PermissionSet.SObjectType => SEL_PermissionSet.class,
            PermissionSetAssignment.SObjectType => SEL_PermissionSetAssignments.class,
            PP_PartnerApplication__c.SObjectType => SEL_PartnerApplication.class,
            PP_PartnershipOpportunity__c.SObjectType => SEL_PartnershipOpportunities.class,
            ProcessInstanceStep.SObjectType => SEL_ProcessInstanceSteps.class,
            ProcessInstanceWorkItem.SObjectType => SEL_ProcessInstanceWorkItems.class,
            Product_Distribution__c.SObjectType => SEL_ProductDistributions.class,
            Product2.SObjectType => SEL_Products2.class,
            Profile.SObjectType => SEL_Profiles.class,
            Promotion__c.SObjectType => SEL_Promotions.class,
            Promotion_Translation__c.SObjectType => SEL_PromotionTranslations.class,
            Provider__c.SObjectType => SEL_Providers.class,
            Relationship_Manager__c.SObjectType => SEL_Relationship_Manager.class,
            Report.SObjectType => SEL_Report.class,
            Resourcing__c.SObjectType => SEL_Resourcing.class,
            SB_Product__c.SObjectType => SEL_Products.class,
            SBG_Footer_Item__mdt.SObjectType => SEL_MALL_FooterItems.class,
            Service_Type__c.SObjectType => SEL_ServiceTypes.class,
            Shortcut__c.SObjectType => SEL_Shortcuts.class,
            Subscribed_Solutions__c.SObjectType => SEL_SubscribedSolutions.class,
            Success_Story__c.SObjectType => SEL_SuccessStories.class,
            SVC_SupportServiceTypes__c.SObjectType => SEL_SupportServiceType.class,
            Tag__c.SObjectType => SEL_Tags.class,
            Tag_Business_Event__c.SobjectType => SEL_MALL_TagEvents.class,
            Tag_Link__c.SObjectType => SEL_TagLinks.class,
            Tag_Offering__c.SObjectType => SEL_TagOfferings.class,
            Tag_Promotion__c.SObjectType => SEL_TagPromotions.class,
            Tag_Provider__c.SObjectType => SEL_TagProviders.class,
            Tag_Success_Story__c.SObjectType => SEL_TagSuccessStories.class,
            Task.SobjectType => SEL_Tasks.class,
            User.SObjectType => SEL_Users.class,
            User_Preference__c.SObjectType => SEL_UserPreferences.class,
            UserRole.SObjectType => SEL_UserRole.class            
        }
    );


    public static final fflib_Application.ServiceFactory service = new fflib_Application.ServiceFactory(
        new Map<Type, Type>{
            IB_SRV_GatewayAPI_GetCCAP.IService.class => IB_SRV_GatewayAPI_GetCCAP.class,
            OAuth_ESB_Gateway.IService.class => OAuth_ESB_Gateway.class,
            OSB_SRV_ActionFailureHandler.IService.class => OSB_SRV_ActionFailureHandler.class,
            OSB_SRV_ActionFailureHandler.IService.class => OSB_SRV_ActionFailureHandler.class,
            OSB_SRV_ApiConnect.IService.class => OSB_SRV_ApiConnect.class,
            OSB_SRV_ApiConnect.IService.class => OSB_SRV_ApiConnect.class,
            OSB_SRV_EmailBuilder.IService.class => OSB_SRV_EmailBuilder.class,
            OSB_SRV_EmailSender.IService.class => OSB_SRV_EmailSender.class,
            OSB_SRV_NewCase.IService.class => OSB_SRV_NewCase.class,
            OSB_SRV_NewCase.IService.class => OSB_SRV_NewCase.class,
            OSB_SRV_NoknokIntegration.IService.class => OSB_SRV_NoknokIntegration.class,
            OSB_SRV_OnboardingHandler.IService.class => OSB_SRV_OnboardingHandler.class,
            OSB_SRV_OnboardingHandler.IService.class => OSB_SRV_OnboardingHandler.class,
            OSB_SRV_PingIntegration.IService.class => OSB_SRV_PingIntegration.class,
            OSB_SRV_PingIntegration.IService.class => OSB_SRV_PingIntegration.class,
            OSB_SRV_SolutionAPI.IService.class => OSB_SRV_SolutionAPI.class,
            PBB_SRV_GatewayApi_AccountInformation.IService.class => PBB_SRV_GatewayApi_AccountInformation.class,
            PBB_SRV_GatewayApi_Conversations.IService.class => PBB_SRV_GatewayApi_Conversations.class,
            PBB_SRV_GatewayApi_GetCustomers.IService.class => PBB_SRV_GatewayApi_GetCustomers.class,
            PBB_SRV_GatewayAPI_GetNotes.IService.class => PBB_SRV_GatewayAPI_GetNotes.class,
            PBB_SRV_GatewayAPI_GetTransactions.IService.class => PBB_SRV_GatewayAPI_GetTransactions.class,
            PBB_SRV_GatewayAPI_RiskAndFacilities.IService.class => PBB_SRV_GatewayAPI_RiskAndFacilities.class,
            PBB_SRV_GatewayRequestProvider.IService.class => PBB_SRV_GatewayRequestProvider.class,
            PP_SRV_NewCase.IService.class => PP_SRV_NewCase.class,
            PP_SRV_PartnerApplication.IService.class => PP_SRV_PartnerApplication.class,
            PP_SRV_PING.IService.class => PP_SRV_PING.class,
            SRV_Customer1ClientSynchronizer.IService.class => SRV_Customer1ClientSynchronizer.class,
            SRV_Document.IService.class => SRV_Document.class,
            SRV_Encryption.IService.class => SRV_Encryption.class,
            SRV_Entitlement.IService.class => SRV_Entitlement.class,
            SRV_GatewayAPI_Ens.IService.class => SRV_GatewayAPI_Ens.class,
            SRV_HttpRequestSender.IService.class => SRV_HttpRequestSender.class,
            SRV_Logger.IService.class => SRV_Logger.class,
            SRV_NBACRealEstateFinance.IService.class => SRV_NBACRealEstateFinance.class,
            SRV_OTP.IService.class => SRV_OTP.class,
            SRV_User.IService.class => SRV_User.class
        }
    );


    public static final List<SObjectType> unitOfWorkRegisteredTypes = new List<SObjectType>{
        User.SObjectType,
        UserRole.SObjectType,
        Account.SObjectType,
        KYC_Status__c.SObjectType,
        Client_Capital_and_Revenue__c.SObjectType,
        Client_Operating_Countries__c.SObjectType,
        Contact.SObjectType,
        Child_products__c.SObjectType,
        Gift_Expense_Log__c.SObjectType,
        Key_Client_Contact__c.SObjectType,
        Knowledge_Entitlement_Exception__c.SObjectType,
        Lead.SObjectType,
        Campaign.SObjectType,
        CampaignMember.SObjectType,
        Campaign_Member_Host__c.SObjectType,
        Campaign_Hosts__c.SObjectType,
        Service_Type__c.SObjectType,
        Case.SObjectType,
        Call_Report__c.SObjectType,
        Call_Report__Share.SObjectType,
        Agenda__c.SObjectType,
        Call_Report_Attendees__c.SObjectType,
        Topic__c.SObjectType,
        Opportunity.SObjectType,
        OpportunityTeamMember.SObjectType,
        SB_Product__c.SObjectType,
        Resourcing__c.SObjectType,
        Cashflow__c.SObjectType,
        Distribution_Client__c.SObjectType,
        Credit_Line__c.SObjectType,
        Custom_Client_Team__c.SObjectType,
        Non_User_Client_Team__c.SObjectType,
        SA_Contact_Team_Member__c.SObjectType,
        Event.SObjectType,
        Task.SObjectType,
        Business_Assessment__c.SObjectType,
        NBAC_Financial_Analysis__c.SObjectType,
        Assessment_Opportunity__c.SObjectType,
        Ecosystem__c.SObjectType,
        Ecosystem_Entity__c.SObjectType,
        Estimated_Revenue_Schedule__c.SObjectType,
        Revenue_and_Profitability__c.SObjectType,
        Client_Plan__c.SObjectType,
        Global_Client_Revenue__c.SObjectType,
        FinServ__Revenue__c.SObjectType,
        Credit_Risk_Appetite__c.SObjectType,
        Client_Financial_Analysis__c.SObjectType,
        Draw_Down_Profile__c.SObjectType,
        Client_Satisfaction_Index__c.SObjectType,
        Account_Information__c.SObjectType,
        Share_Wallet__c.SObjectType,
        Share_of_Wallet_Product__c.SObjectType,
        Account_Application__c.SObjectType,
        AOB_Products__c.SObjectType,
        ArticleNews__c.SObjectType,
        PermissionSet.SObjectType,
        PermissionSetAssignment.SObjectType,
        NewsBookmark__c.SObjectType,
        Onboarding_Application__c.SObjectType,
        Client_Data_Change__c.SObjectType,
        Notification__c.SObjectType,
        Vote.SObjectType,
        Document.SObjectType,
        Subscribed_Solutions__c.SObjectType,
        Log__c.SObjectType,
        Log_Event__e.SObjectType,
        Conversation__c.SObjectType,
        PBB_Note_Types__c.SObjectType,
        CI_Gem__c.SObjectType,
        CI_News_Cache__c.SObjectType,
        Option_List_Item__c.SObjectType,
        Investor_Participation__c.SObjectType,
        Product_Distribution__c.SObjectType,
        Application_Document__c.SObjectType,
        CurrencyType.SObjectType,
        Conversation_Value__c.SObjectType,
        EAP_AppEvent__c.SObjectType,
        EAP_Meeting__c.SObjectType,
        EAP_Attendee__c.SObjectType,
        EAP_Attendance__c.sObjectType,
        EAP_Document__c.SObjectType,
        EAP_EventVenue__c.sObjectType,
        EAP_MeetingAvailability__c.sObjectType,
        EAP_TravelInformation__c.SObjectType,
        EAP_Venue__c.SObjectType,
        Relationship_Manager__c.SObjectType,
        ContentDocumentLink.sObjectType,
        ContentVersion.sObjectType,
        ContentNote.sObjectType,
        ContentDocument.sObjectType,
        Report.SObjectType,
		AuthorizationForm.SObjectType,
		AuthorizationFormText.SObjectType,
		AuthorizationFormConsent.SObjectType
        CustomNotificationType.SObjectType,
        PP_PartnerApplication__c.SObjectType,
        PP_PartnershipOpportunity__c.SObjectType,
        AccountContactRelation.SObjectType,
        CMN_Account_Contact_Relationship_Detail__c.SObjectType,
        AOB_ReferencedData__c.SObjectType,
        Individual.SObjectType,
        DataUsePurpose.SObjectType,
        ContactPointTypeConsent.SObjectType,
        NBAC_Real_Estate_Finance__c.SObjectType,
        ECST_Main_Quorum__mdt.SObjectType,
        ECST_Additional_Quorum__mdt.SObjectType,
        Insight__c.SObjectType,
        Insight_Client_Relationship__c.SObjectType,
        Insight_Action__c.SObjectType,
        AutoEmailNotification__c.SObjectType 
    };

    public static final fflib_Application.UnitOfWorkFactory unitOfWork = new fflib_Application.UnitOfWorkFactory(
            unitOfWorkRegisteredTypes
    );


    public static final fflib_Application.DomainFactory domain = new fflib_Application.DomainFactory(
        ORG_Application.selector,
        new Map<SObjectType, Type>{
            Resourcing__c.SObjectType => DMN_Resourcing.Constructor.class,
            SB_Product__c.SObjectType => DMN_SB_Product.Constructor.class,
            Assessment_Opportunity__c.SObjectType => DMN_Assessment_Opportunity.Constructor.class,
            Business_Assessment__c.SObjectType => DMN_BusinessAssessment_Injectable.Constructor.class
        }
    );
}