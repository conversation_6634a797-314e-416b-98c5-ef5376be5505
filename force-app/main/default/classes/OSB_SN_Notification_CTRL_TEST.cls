/**
 * @description Test class for OSB_SN_Notification_CTRLL_S
 * <AUTHOR>
 * @UserStory SFP-25120
 * @date Nov 2023
 */
@isTest
public class OSB_SN_Notification_CTRL_TEST {
	private static final String TEST_USER_NAME = '<EMAIL>';
    private static final String TEST_ADMIN_NAME = '<EMAIL>';
    private static final String TEST_CONTACT_EMAIL = '<EMAIL>';
    private static final String TEST_CONTACT_ACCESS_ROLE = 'Authorised Person';
    private static final String TEST_CONTACT_FIRST_NAME = 'Test';
    private static final String TEST_CONTACT_LAST_NAME = 'Manager';
    private static final String TEST_CONTACT_PING_ID = '123456789';

    /**
     *@description return test user
    */
    private static User testUser {
        get {
            if(testUser == null) {
                testUser = [SELECT Id, ContactId, Email, Phone, Name FROM User WHERE Username = :TEST_USER_NAME LIMIT 1];
            }
            return testUser;
        }
        set;
    }
    /**
     *@description return test contact
    */
    private static Contact testContact {
        get {
            if(testContact == null) {
                testContact = [SELECT Id, FirstName, LastName, Name, Email, Ping_Id__c FROM Contact WHERE Email = :TEST_CONTACT_EMAIL LIMIT 1];
            }
            return testContact;
        }
        set;
    }

    /**
     *@description setup method to create test user and contact
    */
    @TestSetup
    static void setup() {
        fflib_SObjectUnitOfWork uow = ABS_ObjectBuilderBase.getNewUnitOfWorkWithKnowledge();
        Contact communityContact = (Contact) new BLD_Contact(uow)
            .name(TEST_CONTACT_FIRST_NAME, TEST_CONTACT_LAST_NAME)
            .communityAccessManager(new BLD_Contact(uow).communityAccessRole(TEST_CONTACT_ACCESS_ROLE))
            .email(TEST_CONTACT_EMAIL)
            .ownerId(UserInfo.getUserId())
            .communityAccessRole(TEST_CONTACT_ACCESS_ROLE)
            .pingId(TEST_CONTACT_PING_ID)
            .account(new BLD_Account(uow))
            .getRecord();
        uow.commitWork();
        User onehubAdmin;
        System.runAs(new User(Id = UserInfo.getUserId())) {
            onehubAdmin = (User) new BLD_USER(uow)
                .useOneHubAdmin()
                .firstName(TEST_ADMIN_NAME)
                .getRecord();
            User communityUser = (User) new BLD_USER(uow)
                .profile(DMN_Profile.ONE_HUB_COMMUNITY)
                .userName(TEST_USER_NAME)
                .email(testContact.Email)
                .firstName(TEST_CONTACT_FIRST_NAME)
                .lastName(TEST_CONTACT_LAST_NAME)
                .contactId(testContact.Id)
                .getRecord();
            uow.commitWork();
        }
    }
    /**
     * @description  shouldReturnKnowledgeArticle
     */
    @isTest
    static void shouldReturnKnowledgeArticle(){
        List<Schema.SObjectType> mySObjects = new Schema.SObjectType[]{Knowledge__kav.SObjectType};
        fflib_SObjectUnitOfWork uow = new fflib_SObjectUnitOfWork(mySObjects);
        BLD_Knowledge knowledgeRecord = new BLD_Knowledge(uow);
        knowledgeRecord.setOSBData();
        uow.commitWork();
        Knowledge__kav knowlegeArticleRecord = OSB_SN_Notification_CTRL.getKnowledgeArticleByUrlName('Testname');
        Assert.areNotEqual(null, knowlegeArticleRecord, 'It should return the record');
    }
    /**
     * @description  sendNotificationToDealer
     */
    @isTest
    static void sendNotificationToDealer(){
        Insight__c insight = AKI_TESTDATA.createInsightWithOwner(testUser.Id);
        Test.setMock(HTTPCalloutMock.class, new AKI_InsightsMock(testContact.Id));
        Test.startTest();
        OSB_SN_Notification_CTRL.sendNotificationToDealer(insight.Id);
		List<Insight_Action__c> insightActionRecords = new SEL_InsightActions().selectWithoutCondition();
        Assert.areEqual('Contact Dealer', insightActionRecords[0].Action_Type__c,'Insight Action record should be created with action type as Contact Dealer');
        Test.stopTest();
    }
    /**
     * @description  shouldGetPdfFileAsBase64String
     */
    @isTest
    static void shouldGetPdfFileAsBase64String(){
        List<Insight__c> insightList = AKI_TESTDATA.createInsights(5);
        Test.startTest();
        PageReference pageRef = Page.OSB_SN_DownloadNudge;
        Test.setCurrentPage(pageRef); 

        String base64String = OSB_SN_Notification_CTRL.getPdfFileAsBase64String(insightList[0].Id);
        Assert.areNotEqual(null, base64String,'It should return base64 file as string with nudge details');
        Test.stopTest();
    }
    
    /**
     * @description  shouldReturnUnreadInsights
     */
    @isTest
    static void shouldReturnUnreadInsights(){
        
        Test.startTest();
        
        List<Schema.SObjectType> mySObjects = new Schema.SObjectType[]{Insight__c.SObjectType,Insight_Client_Relationship__c.SObjectType};
        fflib_SObjectUnitOfWork uow = new fflib_SObjectUnitOfWork(mySObjects);
        
        BLD_Insight insight = new BLD_Insight(uow).category('FX TIPS');
        insight.subCategory('Trade Cross Sell');
        insight.owner(UserInfo.getUserId());
        insight.snoozed(false);
        insight.eventDate(Date.today());
        insight.persona('Client');
        insight.insight('Test Insight');
        insight.clientCoordinator(UserInfo.getUserId());
        uow.commitWork();
		List<Insight__c> insightList = new SEL_Insights().selectWithoutCondition();
        
        BLD_InsightClientRelationship insightClientRelationShip = new BLD_InsightClientRelationship(uow);
        insightClientRelationShip.setContact(testContact.Id);
        insightClientRelationShip.setInsight(insightList[0].Id);
        uow.commitWork();
        System.runAs(new User(Id = testUser.Id)){
            OSB_SN_Notification_CTRL.Result returnResult= OSB_SN_Notification_CTRL.getUnreadInsights();
            Assert.areEqual(true,returnResult.insightList.size() > 0, 'Was expecting to find at least one unread insight');
        }
        Test.stopTest();
    }

    /**
     * @description  shouldReturnSnoozedInsights
     */
    @isTest
    static void shouldReturnSnoozedInsights(){
        
        Test.startTest();
        
        List<Schema.SObjectType> mySObjects = new Schema.SObjectType[]{Insight__c.SObjectType,Insight_Client_Relationship__c.SObjectType};
        fflib_SObjectUnitOfWork uow = new fflib_SObjectUnitOfWork(mySObjects);
        
        BLD_Insight insight = new BLD_Insight(uow).category('FX TIPS');
        insight.owner(UserInfo.getUserId());
        insight.subCategory('Trade Cross Sell');
        insight.snoozed(true);
        insight.eventDate(Date.today());
        insight.persona('Client');
        insight.insight('Test Insight');
        insight.clientCoordinator(UserInfo.getUserId());
        uow.commitWork();
		List<Insight__c> insightList = new SEL_Insights().selectWithoutCondition();
        
        BLD_InsightClientRelationship insightClientRelationShip = new BLD_InsightClientRelationship(uow);
        insightClientRelationShip.setContact(testContact.Id);
        insightClientRelationShip.setInsight(insightList[0].Id);
        uow.commitWork();
        System.runAs(new User(Id = testUser.Id)){
            OSB_SN_Notification_CTRL.Result returnResult= OSB_SN_Notification_CTRL.getSavedInsights();
            Assert.areEqual(true,returnResult.insightList.size() > 0, 'Was expecting to find at least one snoozed insight');
        }
        Test.stopTest();
    }
    
    /**
     * @description  shouldReturnAllMyInsights
     */
    @isTest
    static void shouldReturnAllMyInsights(){
        
        Test.startTest();
        List<Schema.SObjectType> mySObjects = new Schema.SObjectType[]{Insight__c.SObjectType,Insight_Client_Relationship__c.SObjectType};
        fflib_SObjectUnitOfWork uow = new fflib_SObjectUnitOfWork(mySObjects);
        
        BLD_Insight insight = new BLD_Insight(uow).category('FX TIPS');
        insight.subCategory('Trade Cross Sell');
        insight.owner(UserInfo.getUserId());
        insight.snoozed(false);
        insight.eventDate(Date.today());
        insight.persona('Client');
        insight.insight('Test Insight');
        insight.clientCoordinator(UserInfo.getUserId());
        uow.commitWork();
        List<Insight__c> insightList = new SEL_Insights().selectWithoutCondition();
        
        BLD_InsightClientRelationship insightClientRelationShip = new BLD_InsightClientRelationship(uow);
        insightClientRelationShip.setContact(testContact.Id);
        insightClientRelationShip.setInsight(insightList[0].Id);
        uow.commitWork();
        System.runAs(new User(Id = testUser.Id)){
            OSB_SN_Notification_CTRL.Result returnResult= OSB_SN_Notification_CTRL.getInsightsData();
            Assert.areEqual(true,returnResult.insightList.size() > 0, 'Was expecting to find at least one unread insight');
        }
        Test.stopTest();
    }

    /**
     * @description  shouldSnoozeTheNudge
     */
    @isTest
    static void shouldSnoozeTheNudge(){
        Insight__c insight = AKI_TESTDATA.createInsight();
        Test.setMock(HTTPCalloutMock.class, new AKI_InsightsMock(testContact.Id));
        Test.startTest();
        OSB_SN_Notification_CTRL.saveOrUnsaveNudge(insight.Id,true,insight.External_Lead_ID__c);
		List<Insight__c> insightRecords = new SEL_Insights().selectWithoutCondition();
        Assert.areEqual(true, insightRecords[0].Is_Snoozed__c,'Nudge should be snoozed');
        Test.stopTest();
    }
    
    /**
     * @description  shouldMarkNudgeAsRead
     */
    @isTest
    static void shouldMarkNudgeAsRead(){
        
        Insight__c insight = AKI_TESTDATA.createInsight();
        Insight_Client_Relationship__c insightClientRelationship = AKI_TESTDATA.createInsightClientRelationshipRecord(insight.Id,testContact.Id);
        Test.setMock(HTTPCalloutMock.class, new AKI_InsightsMock(testContact.Id));
        Test.startTest();
        System.runAs(new User(Id = testUser.Id)){
		OSB_SN_Notification_CTRL.markNudgeAsRead(insight.Id,insight.External_Lead_ID__c);
		List<Insight_Client_Relationship__c> insightClientRelatoinshipList = new SEL_InsightClientRelationships().selectWithoutCondition();
        Assert.areEqual(false,insightClientRelatoinshipList[0].Is_Unread__c, 'Nudge should be read');
       }
        Test.stopTest();
    }

}