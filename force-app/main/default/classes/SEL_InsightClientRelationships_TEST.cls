/**
 * @description test class for SEL_Insights class
 * <AUTHOR>
 * @UserStory SFP-25120
 * @date DEC 2023
 */
@IsTest(IsParallel=true)
public class SEL_InsightClientRelationships_TEST {
@IsTest
    static void shouldSelectActiveInsightClientRelationships() {
        Test.startTest();
        new SEL_InsightClientRelationships().selectActiveInsightClientRelationships(new Set<Id>(),20,20);
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Insight__r.Persona__c = \'Client\' AND Insight__r.Event_Date__c = LAST_N_DAYS:20 AND Insight__r.Opportunity__c = null AND Insight__r.Is_Expired__c=false AND Insight__r.Is_Snoozed__c = false AND Insight__r.Is_Provided_<PERSON>back__c=false'), 'Condition used Insight persona, event date, opportunity, is snoozed and is providede feedback.');
    }

    @IsTest
    static void shouldSelectByContactIdAndIsUnread() {
        Test.startTest();
        new SEL_InsightClientRelationships().selectByContactIdAndIsUnread(new Set<String>{'123'},false);
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Is_Unread__c =:isUnread AND Insight__r.Persona__c = \'Client\' AND Insight__r.Opportunity__c = null AND Insight__r.Is_Expired__c=false AND Insight__r.Is_Provided_Feedback__c=false AND Contact__c IN : contactIds'), 'Condition used IsUnread, contact id set and Insight persona, event date, opportunity, is snoozed and is providede feedback.');
    }
    @IsTest
    static void shouldSelectByContactIdAndIsUnreadWoSharing() {
        Test.startTest();
        new SEL_InsightClientRelationships().selectByContactIdAndIsUnreadWoSharing(new Set<String>{'123'},false);
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Is_Unread__c =:isUnread AND Insight__r.Persona__c = \'Client\' AND Insight__r.Opportunity__c = null AND Insight__r.Is_Expired__c=false AND Insight__r.Is_Provided_Feedback__c=false AND Contact__c IN : contactIds'), 'Condition used IsUnread, contact id set and Insight persona, event date, opportunity, is snoozed and is providede feedback.');
    }
    @IsTest
    static void shouldSelectByContactIdAndIsSnoozed() {
        Test.startTest();
        new SEL_InsightClientRelationships().selectByContactIdAndIsSnoozed(new Set<String>{'123'},false);
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Insight__r.Is_Snoozed__c =:isSnoozed AND Insight__r.Persona__c = \'Client\' AND Insight__r.Opportunity__c = null AND Insight__r.Is_Expired__c=false AND Insight__r.Is_Provided_Feedback__c=false AND Contact__c IN : contactIds'), 'Condition used Insight Is Snoozed and Insight persona, event date, opportunity and is providede feedback.');
    }
    
    @IsTest
    static void shouldSelectByContactIdAndIsSnoozedWoSharing() {
        Test.startTest();
        new SEL_InsightClientRelationships().selectByContactIdAndIsSnoozedWoSharing(new Set<String>{'123'},false);
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Insight__r.Is_Snoozed__c =:isSnoozed AND Insight__r.Persona__c = \'Client\' AND Insight__r.Opportunity__c = null AND Insight__r.Is_Expired__c=false AND Insight__r.Is_Provided_Feedback__c=false AND Contact__c IN : contactIds'), 'Condition used Insight Is Snoozed, contact id set and Insight persona, event date, opportunity and is providede feedback.');
    }
    
    @IsTest
    static void shouldSelectByContactId() {
        Test.startTest();
        new SEL_InsightClientRelationships().selectByContactId(new Set<String>());
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Insight__r.Persona__c = \'Client\' AND Insight__r.Opportunity__c = null AND Insight__r.Is_Expired__c=false AND Insight__r.Is_Provided_Feedback__c=false AND Contact__c IN : contactIds'), 'Condition used contact id set and Insight persona, event date, opportunity, is snoozed and is providede feedback.');
    }
    @IsTest
    static void shouldSelectByContactIdWoSharing() {
        Test.startTest();
        new SEL_InsightClientRelationships().selectByContactIdWoSharing(new Set<String>());
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Contact__c IN : contactIds'), 'Condition used contact id set');
    }
    
    @IsTest
    static void shouldSelectByContactIdAndInsightId() {
        Test.startTest();
        new SEL_InsightClientRelationships().selectByContactIdAndInsightId(new Set<String>(),new Set<String>());
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Contact__c IN : contactIds AND Insight__c IN : insightIds'), 'Condition used contact id set and insight id set');
    }
    @IsTest
    static void shouldSelectByContactIdAndInsightIdWoSharing() {
        Test.startTest();
        new SEL_InsightClientRelationships().selectByContactIdAndInsightIdWoSharing(new Set<String>(),new Set<String>());
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Contact__c IN : contactIds AND Insight__c IN : insightIds'), 'Condition used contact id set and insight id set');
    }
    
}