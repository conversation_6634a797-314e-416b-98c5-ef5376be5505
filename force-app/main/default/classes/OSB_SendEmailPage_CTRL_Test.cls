/**
 * @description Test class for OSB_SendEmailPage_CTRL
 *
 * <AUTHOR> (<EMAIL>)
 * @date May 2020
 *
 * <AUTHOR>
 * @description Update to test method to accomodate to flow changes and use asserts.
 * @UserStory SFP-41334
 * @date August 2024
 *
 **/
@IsTest(IsParallel=true)
public class OSB_SendEmailPage_CTRL_Test {
    @TestSetup
    static void setup() {
        List<OSB_URLs__c> osbUrls = TEST_DataFactory.getOsbUrls();
        insert osbUrls;
    }

    @IsTest
    static void shouldSendEmailApEmailInvite() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        fflib_ISObjectUnitOfWork uowMock = new fflib_SObjectMocks.SObjectUnitOfWork(
            mocks
        );
        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(
            SEL_Contacts.class
        );

        Contact accessManager = (Contact) new BLD_Contact()
            .setOSBDefaultData(
                DMN_Contact.OSB_COMMUNITY_ACCESS_ROLE_AP,
                DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_APPROVED
            )
            .recordTypeId(
                UTL_RecordType.getRecordTypeId(
                    DMN_Contact.OBJ_NAME,
                    DMN_Contact.RTD_BANK
                )
            )
            .mock();

        mocks.startStubbing();
        mocks.when(contactsSel.sObjectType()).thenReturn(Contact.SObjectType);
        mocks.when(contactsSel.selectById(new Set<Id>{ accessManager.Id }))
            .thenReturn(new List<Contact>{ accessManager });
        mocks.stopStubbing();

        ORG_Application.selector.setMock(contactsSel);
        ORG_Application.unitOfWork.setMock(uowMock);

        ApexPages.StandardController controller = new ApexPages.StandardController(
            accessManager
        );
        OSB_SendEmailPage_CTRL osbSendEmailPageCtrl = new OSB_SendEmailPage_CTRL(
            controller
        );

        Test.startTest();
        osbSendEmailPageCtrl.sendApEmailInvite();
        Test.stopTest();

        ((fflib_ISObjectUnitOfWork) mocks.verify(uowMock, 1)).commitWork();
        fflib_ArgumentCaptor argument = fflib_ArgumentCaptor.forClass(
            fflib_ISObjectUnitOfWork.class
        );
        ((fflib_ISObjectUnitOfWork) mocks.verify(uowMock, 1))
            .registerDirty((Contact) argument.capture());
        Contact updatedContact = (Contact) argument.getValue();
        Assert.areEqual(
            DMN_Contact.OSB_COMMUNITY_ACCESS_STATUS_INVITE_SENT,
            updatedContact.OSB_Community_Access_Status__c,
            'Contact has an updated community status to invite sent.'
        );
        Assert.areEqual(
            true,
            String.isNotEmpty(updatedContact.Onehub_Community_URL__c),
            'Contact has an updated community status to invite sent.'
        );
    }
    @IsTest
    static void testEncryptEmail() {
        // Arrange
        String testEmail = '<EMAIL>';
        Contact testContact = new Contact(FirstName = 'Test', LastName = 'User', Email = testEmail);
        insert testContact;
        
        // Act
        Test.startTest();
        String encryptedEmail = OSB_SendEmailPage_CTRL.encryptEmail(testEmail);
        Test.stopTest();
        
        // Assert
        Blob emailBlob = Blob.valueOf(testEmail);
        Blob expectedHash = Crypto.generateDigest('SHA-256', emailBlob);
        String expectedEncryptedEmail = EncodingUtil.convertToHex(expectedHash);
        
       Assert.areEqual(expectedEncryptedEmail, encryptedEmail, 'The encrypted email should match the expected value.');
    }
}