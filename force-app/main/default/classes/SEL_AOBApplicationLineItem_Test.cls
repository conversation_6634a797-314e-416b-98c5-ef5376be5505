/**
 * Test class for SEL_AOBApplicationLineItem
 * 
 * <p>SFP-8830 and other Adobe tagging user stories</p> 
 * 
 * <AUTHOR>
 * 
 * @date 14 January 2022
 */
@IsTest
public class SEL_AOBApplicationLineItem_Test { 

    /**
    * Test method for SEL_AOBApplicationLineItem.getSObjectFieldList
    * Ensure that a field list is returned
    */    
    @IsTest
    private static void getSObjectFieldList_Test(){
        User admin = AOB_DAL_TestFactory.createUser(AOB_TestConstants.SYSTEM_ADMINISTRATOR, true);
        System.runAs(admin){
            Test.startTest();
            SEL_AOBApplicationLineItem selAppItem = new SEL_AOBApplicationLineItem();
            Assert.areNotEqual(0,selAppItem.getSObjectFieldList().size());
            Test.stopTest();
        }    
    } 

    /**
    * Test method for SEL_AOBApplicationLineItem.getSObjectType
    * Ensure that the correct object type is returned
    */    
    @IsTest
    private static void getSObjectType_Test(){
        User admin = AOB_DAL_TestFactory.createUser(AOB_TestConstants.SYSTEM_ADMINISTRATOR, true);
        System.runAs(admin){
            Test.startTest();
            SEL_AOBApplicationLineItem selAppItem = new SEL_AOBApplicationLineItem();
            Schema.SObjectType appItemType = selAppItem.getSObjectType();
            Test.stopTest();
            Assert.areEqual(AOB_ApplicationLineItem__c.sObjectType,appItemType);
        }
    }   

    /**
    * Test method for SEL_AOBApplicationLineItem.selectWithoutCondition
    * Ensure that an application line item is returned
    */
    @IsTest
    private static void selectWithoutCondition_Test(){
        User communityUser = AOB_DAL_TestFactory.createCommunityUser();
        User admin = AOB_DAL_TestFactory.createUser(AOB_TestConstants.SYSTEM_ADMINISTRATOR, true);
        System.runAs(admin){
            AOB_Application__c newApplication = AOB_DAL_TestFactory.createApplication(communityUser);
            Product2 newProduct = AOB_DAL_TestFactory.createProduct('MyMoBiz');
            AOB_ApplicationLineItem__c newApplicationLineItem = AOB_DAL_TestFactory.createApplicationLineItem(newProduct.Id, newApplication.Id);
            Test.startTest();
            List<AOB_ApplicationLineItem__c> appItems = new SEL_AOBApplicationLineItem().selectWithoutCondition();
            Test.stopTest();
            Assert.areNotEqual(0,appItems.size());  
        }
    }      

    /**
    * Test method for SEL_AOBApplicationLineItem.selectAppLineItemsById
    * Ensure that an application line item is returned
    */
    @IsTest
    private static void selectAppLineItemsById_Test(){
        User communityUser = AOB_DAL_TestFactory.createCommunityUser();
        User admin = AOB_DAL_TestFactory.createUser(AOB_TestConstants.SYSTEM_ADMINISTRATOR, true);
        System.runAs(admin){
            AOB_Application__c newApplication = AOB_DAL_TestFactory.createApplication(communityUser);
            Product2 newProduct = AOB_DAL_TestFactory.createProduct('MyMoBiz');
            AOB_ApplicationLineItem__c newApplicationLineItem = AOB_DAL_TestFactory.createApplicationLineItem(newProduct.Id, newApplication.Id);
            Test.startTest();
            List<AOB_ApplicationLineItem__c> appItems = new SEL_AOBApplicationLineItem().selectAppLineItemsById(newApplication.Id);
            Test.stopTest();
            Assert.areNotEqual(0,appItems.size());  
        }
    }

    /**
    * Test method for SEL_AOBApplicationLineItem.selectAppLineItemsByIdAndProductCode
    * Ensure that an application line item is returned
    */
    @IsTest
    private static void selectAppLineItemsByIdAndProductCode_Test(){
        User communityUser = AOB_DAL_TestFactory.createCommunityUser();
        User admin = AOB_DAL_TestFactory.createUser(AOB_TestConstants.SYSTEM_ADMINISTRATOR, true);
        System.runAs(admin){
            AOB_Application__c newApplication = AOB_DAL_TestFactory.createApplicationWithLineItems(communityUser);
            Product2 newProduct = AOB_DAL_TestFactory.createProduct('MyMoBiz');
            AOB_ApplicationLineItem__c newApplicationLineItem = AOB_DAL_TestFactory.createApplicationLineItem(newProduct.Id, newApplication.Id);
            Test.startTest();
            List<AOB_ApplicationLineItem__c> appItems = new SEL_AOBApplicationLineItem().selectAppLineItemsByIdAndProductCode(newApplication.Id, '4648');
            Test.stopTest();
            Assert.areNotEqual(0,appItems.size());  
        }
    }

    /**
    * Test method for SEL_AOBApplicationLineItem.selectAppLineItemsByAppIdAndName
    * Ensure that an application line item is returned
    */
    @IsTest
    private static void selectAppLineItemsByAppIdAndName_Test(){
        User communityUser = AOB_DAL_TestFactory.createCommunityUser();
        User admin = AOB_DAL_TestFactory.createUser(AOB_TestConstants.SYSTEM_ADMINISTRATOR, true);
        System.runAs(admin){
            AOB_Application__c newApplication = AOB_DAL_TestFactory.createApplicationWithLineItems(communityUser);
            Product2 newProduct = AOB_DAL_TestFactory.createProduct('MyMoBiz');
            AOB_ApplicationLineItem__c newApplicationLineItem = AOB_DAL_TestFactory.createApplicationLineItem(newProduct.Id, newApplication.Id);
            Test.startTest();
            List<AOB_ApplicationLineItem__c> appItems = new SEL_AOBApplicationLineItem().selectAppLineItemsByAppIdAndName(newApplication.Id, 'MyMoBiz');
            Test.stopTest();
            Assert.areNotEqual(0,appItems.size());  
        }
    }

    /**
    * Test method for SEL_AOBApplicationLineItem.selectAppLineItemsByAppIdAndProductCodes
    * Ensure that an application line item is returned
    */
    @IsTest
    private static void selectAppLineItemsByAppIdAndProductCodes_Test(){
        User communityUser = AOB_DAL_TestFactory.createCommunityUser();
        User admin = AOB_DAL_TestFactory.createUser(AOB_TestConstants.SYSTEM_ADMINISTRATOR, true);
        System.runAs(admin){
            AOB_Application__c newApplication = AOB_DAL_TestFactory.createApplicationWithLineItems(communityUser);
            Product2 newProduct = AOB_DAL_TestFactory.createProduct('MyMoBiz');
            AOB_ApplicationLineItem__c newApplicationLineItem = AOB_DAL_TestFactory.createApplicationLineItem(newProduct.Id, newApplication.Id);
            Test.startTest();
            List<AOB_ApplicationLineItem__c> appItems = new SEL_AOBApplicationLineItem().selectAppLineItemsByAppIdAndProductCodes(newApplication.Id, new List<String>{'4648'});
            Test.stopTest();
            Assert.areNotEqual(0,appItems.size());  
        }
    }
    
}