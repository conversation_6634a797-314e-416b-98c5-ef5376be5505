/**
 * @descripton This service class is used for omitting sharing when creating Authorization Form Consent records
 *
 * <AUTHOR> (<EMAIL>)
 * @date September 2024
 * @UserStory SFP-36886
 */
public without sharing class OSB_SRV_AgencyConsent {
    private static final sbgplatform.rflib_Logger LOGGER = sbgplatform.rflib_LoggerUtil.getFactory().createLogger('OBS_SRV_AgencyConsent');

    /**
	 * @description Creates Authorization Form Consent record linked to a contact and knowledge article
     * @param Map<Object> map containing the details required to create the AuthorizationFormConsent record.
     * 
     * @return String Authorization Form Consent Id
     * 
     **/
    public static void createConsentRecord(Map<String, Object> consentDetails) {
        try {
            Id userId = UserInfo.getUserID();
            Contact userContact = SEL_Contacts.newInstance().selectByUserId(new Set<Id>{ userId })[0];
    
            fflib_ISObjectUnitOfWork uow = ORG_Application.unitOfWork.newInstance(); 
            AuthorizationFormConsent consent = new AuthorizationFormConsent(); 

            consent = populateConsentDetails(consent, userContact, consentDetails);   
            uow.registerNew(consent);

            uow.commitWork();
        } catch(Exception e) {
            LOGGER.error('OBS_SRV_AgencyConsent : createConsentRecord - Exception logged: ', e);
            throw e;
        }
    }
    
    /**
     * @description Populates the details of the given AuthorizationFormConsent record with the provided consent details and user contact information.
     * @param consent The AuthorizationFormConsent object to be populated
     * @param userContact The Contact object representing the user giving consent
     * @param consentDetails A Map containing the details to populate the consent record
     * 
     * @return String Authorization Form Consent Id
     * 
     */
    private static AuthorizationFormConsent populateConsentDetails(AuthorizationFormConsent consent, Contact userContact, Map<String, Object> consentDetails) {
        consent.AuthorizationFormTextId = (Id) consentDetails.get('authorizationFormTextId');
        consent.ConsentCapturedDateTime = System.now();
        consent.ConsentCapturedSource = 'OneHub Application Tile';
        consent.ConsentCapturedSourceType = 'Web';
        consent.ConsentGiverId = userContact.Id;
        consent.Knowledge__c = (Id) consentDetails.get('knowledgeId');
        consent.Name = 'OneHub - Agency Disclaimer | ' + userContact.Name + ' | ' + (String) consentDetails.get('knowledgeName');
        consent.Status = 'Signed';
        return consent;
    }
}