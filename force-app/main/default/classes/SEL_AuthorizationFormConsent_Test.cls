/**
 * @description Test class for SEL_AuthorizationForm
 *
 * <AUTHOR> (<EMAIL>)
 * @date September 2024
 * @UserStory SFP-36886
 */
@isTest
private class SEL_AuthorizationFormConsent_Test {

    @IsTest
    static void shouldSelectById() {
        Test.startTest();
        SEL_AuthorizationFormConsent.newInstance().selectById(new Set<Id>());
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Id IN :idSet'), 'Condition used consent id set.');
    }

    @IsTest
    static void shouldSelectByContactId() {
        Test.startTest();
        SEL_AuthorizationFormConsent.newInstance().selectByContactId(new Set<Id>());
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('ConsentGiverId IN :idSet'), 'Condition used contact id set.');
    }
}