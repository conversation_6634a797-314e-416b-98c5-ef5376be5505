/**
 * <AUTHOR> (<EMAIL>)
 * @description  Test class for OSB_OtpManagement_CTRL
 * @UserStory SFP-38763
 * @date May 2024
 * 
 * @LastModified February 2025
 * <AUTHOR> (<EMAIL>)
 * @UserStory SFP-46065
 * @LastModifiedReason added test data and test methods for retrieveOTPEnablementFirstDevice method
 **/

@isTest
public class OSB_OtpManagement_CTRL_Test {
    private static final String INPUTCODE = 'WHCN7';
    private static final String OTPREASON = 'Device Addition';
    private static  String ACTUALEXCEPTION;
    private static final String DEVELOPERNAME = 'OneHub';
    private static final String FEATURELABEL = 'OTP Enablement';
    private static final String FIRSTDEVICELABEL= 'OTP Enablement First Device';
   
    @TestSetup
    static void setup() {

        List<SObject> customMetadataList = new List<SObject>();
        customMetadataList.addAll(TEST_DataFactory.getOsbUrls());

        OTP_Request_Setting__mdt settings = new OTP_Request_Setting__mdt();
        settings.AttemptLimit__c = 20;
        settings.CodeCharacterLength__c = 5;
        settings.DeveloperName = 'OneHub';
        settings.CodeDurationSeconds__c = 120;
        insert customMetadataList;



    }

    @isTest
    public static void testCustomMetadataRecord() {
        Test.startTest();
        OTP_Request_Setting__mdt record = OSB_OtpManagement_CTRL.getCustomMetadataRecord();
        Test.stopTest();
        Assert.areEqual('OneHub',record.MasterLabel,'OneHub metadata record was returned');
    }

    private static Contact createTestContact() {
        Contact testContact = new Contact();
        testContact.FirstName = 'Tester';
        testContact.LastName = 'TesterSur';
        testContact.Email = '<EMAIL>';
        insert testContact;
        return testContact;
    }

    private static OTPRequest__c createTestOtpRequest(Id relatedRecordId) {
        OTPRequest__c testOtpRequest = new OTPRequest__c();
        testOtpRequest.RelatedRecordID__c = relatedRecordId;
        testOtpRequest.Code__c = 'TEST1';
        testOtpRequest.Requests__c = 1;
        insert testOtpRequest;
        return testOtpRequest;
    }

    @isTest
    public static void testInitialiseOTP() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(SEL_Contacts.class);

        Contact testContact = createTestContact();

        mocks.startStubbing();
        mocks.when(contactsSel.sObjectType()).thenReturn(Contact.SObjectType);
        mocks.when(contactsSel.selectByUserId((Set<Id>) fflib_Match.anyObject())).thenReturn(new List<Contact>{ testContact });
        mocks.stopStubbing();
        ORG_Application.selector.setMock(contactsSel);

        Test.startTest();
        SRV_OTP.Response otpResponse = OSB_OtpManagement_CTRL.initialiseOTP(OTPREASON);
        Test.stopTest();
        Assert.areEqual(
            true,
            otpResponse.isSuccess,
            'OTP successfully created'
        );
    }

    @isTest 
    public static void shouldHandleExceptionOnInitialise(){

        fflib_ApexMocks mocks = new fflib_ApexMocks();
        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(SEL_Contacts.class);

        Contact testContact = createTestContact();
        mocks.startStubbing();
        mocks.when(contactsSel.sObjectType()).thenReturn(Contact.SObjectType);
        mocks.when(contactsSel.selectByUserId((Set<Id>) fflib_Match.anyObject())).thenReturn(new List<Contact>{ testContact });
        mocks.when(contactsSel.selectByUserId((Set<Id>) fflib_Match.anyObject())).thenThrow(new UnexpectedException('This is an unexpected exception'));
        mocks.stopStubbing();
        ORG_Application.selector.setMock(contactsSel);

        Test.startTest();
        try {
            OSB_OtpManagement_CTRL.initialiseOTP(OTPREASON);
        }catch(Exception e){
            Assert.areEqual('Script-thrown exception', e.getMessage(), 'Catch exception for Initialise OTP');
        }
    }

    @isTest
    public static void testIfOTPRecordExist() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        SEL_OTPRequest selectorMock = (SEL_OTPRequest) mocks.mock(
            SEL_OTPRequest.class
        );
        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(
            SEL_Contacts.class
        );
        Contact testContact = createTestContact();

        OTPRequest__c testOtpRequest = createTestOtpRequest(testContact.Id);

        mocks.startStubbing();
        mocks.when(contactsSel.sObjectType()).thenReturn(Contact.SObjectType);
        mocks.when(contactsSel.selectByUserId((Set<Id>) fflib_Match.anyObject())) .thenReturn(new List<Contact>{ testContact });
        mocks.when(selectorMock.sObjectType()).thenReturn(OTPRequest__c.SObjectType);
        mocks.when(selectorMock.selectByRelatedRecordId((String) fflib_Match.anyString())).thenReturn(new List<OTPRequest__c>{ testOtpRequest });
        mocks.stopStubbing();

        ORG_Application.selector.setMock(selectorMock);
        ORG_Application.selector.setMock(contactsSel);

        Test.startTest();
        SRV_OTP.Response otpResponse = OSB_OtpManagement_CTRL.initialiseOTP(OTPREASON);
        OSB_OtpManagement_CTRL.sendOutMailOTP(OTPREASON);
        Test.stopTest();

        List<OTPRequest__c> otpRequests = [SELECT Id FROM OTPRequest__c WHERE RelatedRecordID__c = :testContact.Id];
        Assert.areEqual(1, otpRequests.size(), 'There should be one OTP request record.');
        Assert.areEqual(testOtpRequest.Id, otpRequests[0].Id, 'The OTP request record should match the test OTP request.');
    }

    @isTest
    public static void testValidateWrongOTPCode() {

        Contact testContact = createTestContact();
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        SEL_OTPRequest selectorMock = (SEL_OTPRequest) mocks.mock(SEL_OTPRequest.class);
        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(SEL_Contacts.class);
        SRV_OTP serviceMock = (SRV_OTP) mocks.mock(SRV_OTP.class);
        serviceMock.initialise(DEVELOPERNAME, testContact.Id);
        SRV_OTP.Response mockResponse = new SRV_OTP.Response();

        OTPRequest__c testOtpRequest = createTestOtpRequest(testContact.Id);
        mocks.startStubbing();
        mocks.when(contactsSel.sObjectType()).thenReturn(Contact.SObjectType);
        mocks.when(contactsSel.selectByUserId((Set<Id>) fflib_Match.anyObject())).thenReturn(new List<Contact>{ testContact });
        mocks.when(selectorMock.sObjectType()).thenReturn(OTPRequest__c.SObjectType);
        mocks.when(selectorMock.selectByRelatedRecordId( (String) fflib_Match.anyString())).thenReturn(new List<OTPRequest__c>{ testOtpRequest });
        mocks.when(serviceMock.validateCode(INPUTCODE)).thenReturn(mockResponse.isSuccess);
        mocks.stopStubbing();

        ORG_Application.selector.setMock(selectorMock);
        ORG_Application.selector.setMock(contactsSel);

        Test.startTest();
        try{
           OSB_OtpManagement_CTRL.validateOTPCode(INPUTCODE);
        }catch (OSB_OtpManagement_CTRL.OTPManagementException e) {
            ACTUALEXCEPTION = e.getMessage();
        }
        Test.stopTest();
        Assert.areEqual('Validation Failure' ,ACTUALEXCEPTION,'OTPManagementException for OTP Validation.');
    }

    @isTest 
    public static void shouldHandleExceptionOnValidateOTP(){
        Exception ex;
        Contact testContact = createTestContact();
        fflib_ApexMocks mocks = new fflib_ApexMocks();

        SEL_OTPRequest selectorMock = (SEL_OTPRequest) mocks.mock(SEL_OTPRequest.class);
        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(SEL_Contacts.class);
        SRV_OTP serviceMock = (SRV_OTP) mocks.mock(SRV_OTP.class);

        serviceMock.initialise('DEVELOPERNAME', testContact.Id);
        SRV_OTP.Response mockResponse = new SRV_OTP.Response();
        mockResponse.isSuccess = false;

        OTPRequest__c testOtpRequest = createTestOtpRequest(testContact.Id);
        mocks.startStubbing();
        mocks.when(contactsSel.sObjectType()).thenReturn(Contact.SObjectType);
        mocks.when(contactsSel.selectByUserId((Set<Id>) fflib_Match.anyObject())).thenThrow(new UnexpectedException('This is an unexpected exception'));
        mocks.stopStubbing();

        ORG_Application.selector.setMock(selectorMock);
        ORG_Application.selector.setMock(contactsSel);

        Test.startTest();
        try {
            OSB_OtpManagement_CTRL.validateOTPCode(INPUTCODE);
        }
        catch(Exception e){
            Assert.areEqual('Script-thrown exception',e.getMessage(),'Catch exception for OTP Validation.');
        }
        Test.stopTest();        
    }

    @isTest
    static void shouldCreateCase() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(SEL_Contacts.class);
        SEL_Entitlement entitlementSelector = (SEL_Entitlement) mocks.mock( SEL_Entitlement.class);
        Contact testContact = createTestContact();

        Case newCase = new Case();

        Entitlement testEntitlement = new Entitlement();
        testEntitlement.Name = DMN_Entitlement.DEFAULT_ENTITLEMENT_NAME;

        mocks.startStubbing();
        mocks.when(contactsSel.sObjectType()).thenReturn(Contact.SObjectType);
        mocks.when(contactsSel.selectByUserId((Set<Id>) fflib_Match.anyObject())).thenReturn(new List<Contact>{ testContact });
        mocks.when(entitlementSelector.sObjectType()).thenReturn(Entitlement.sObjectType);
        mocks.when(entitlementSelector.selectByNameWithoutSharing((String) fflib_Match.anyString())).thenReturn(new List<Entitlement>{ testEntitlement });
        mocks.stopStubbing();
        ORG_Application.selector.setMock(contactsSel);
        ORG_Application.selector.setMock(entitlementSelector);

        Test.startTest();
        newCase = OSB_OtpManagement_CTRL.createCase();
        Test.stopTest();
        Assert.areEqual(
            'Suspicious activity detected',
            newCase.Subject,
            'Case for suspicious activity was created'
        );
    }

    @isTest
    static void shouldHandleExceptionInCreateCase() {
        Contact testContact = createTestContact();
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(SEL_Contacts.class);

        mocks.startStubbing();
        mocks.when(contactsSel.selectByUserId((Set<Id>) fflib_Match.anyObject())).thenReturn(new List<Contact>{ testContact });
        mocks.stopStubbing();
        ORG_Application.selector.setMock(contactsSel);        
        Test.startTest();
        try {
            OSB_OtpManagement_CTRL.createCase();
        } catch (Exception e)  {
            Assert.areEqual('Failed to create case',e.getMessage(),'Catch exception create case');
        }
        Test.stopTest();    
    }

    @isTest
    public static void testSendOutMailOTP_Exception() {
     
        Test.startTest();
        try {
            OSB_OtpManagement_CTRL.sendOutMailOTP('TestReason');
        } catch (Exception e) {
            Assert.areEqual('Script-thrown exception', e.getMessage(),'Catch exception for send email');
        } 
      Test.stopTest();
    }
    
    @isTest
    public static void testRetrieveFeature() {
        Test.startTest();
        OSB_Feature_Management__mdt record = OSB_OtpManagement_CTRL.retrieveFeatureManagementRecord();
        Test.stopTest();
        Assert.areEqual(FEATURELABEL,record.MasterLabel,'OTP Enablement metadata record was returned');
    }

    @isTest
    static void testRetrieveOTPEnablementFirstDevice_Success() {
        Test.startTest();
        OSB_Feature_Management__mdt result = OSB_OtpManagement_CTRL.retrieveOTPEnablementFirstDevice();
        Test.stopTest();

        Assert.areEqual(FIRSTDEVICELABEL, result.MasterLabel, 'OTP Enablement For First Device metadata record was returned');
    }
   

}