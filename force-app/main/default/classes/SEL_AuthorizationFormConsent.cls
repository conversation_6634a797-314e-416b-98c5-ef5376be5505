/**
 * @description Selector class for AuthorizationFormConsent SObject
 *
 * <AUTHOR> (<EMAIL>)
 * @date September 2024
 * @UserStory SFP-36886
 */
public inherited sharing class SEL_AuthorizationFormConsent extends fflib_SObjectSelector{
    
    /**
     * @description Creates a new instance of the selector via the application class. This is here to allow unit tests to override
	 * and inject a mock instead of this class or to switch out this class for a new version.
     * 
     * @return SEL_AuthorizationFormConsent class instance
     */
    public static SEL_AuthorizationFormConsent newInstance() {
        return (SEL_AuthorizationFormConsent) ORG_Application.selector
            .newInstance(AuthorizationFormConsent.SObjectType);
    }

    /**
     * @description Returns the SObject type for the selector. This is used to retrieve the sObject name when building the SOQL
     * queries.
     * 
     * @return AuthorizationFormConsent object type
     */
    public SObjectType getSObjectType() {
        return AuthorizationFormConsent.SObjectType;
    }

    /**
     * @description Returns the SObject fields on this sObject
     * 
     * @return SEL_AuthorizationFormConsent fields list
     */
    public List<SObjectField> getSObjectFieldList() {
        return new List<Schema.SObjectField>{
            AuthorizationFormConsent.Id,
            AuthorizationFormConsent.AuthorizationFormTextId,
            AuthorizationFormConsent.ConsentCapturedDateTime,
            AuthorizationFormConsent.ConsentCapturedSource,
            AuthorizationFormConsent.ConsentCapturedSourceType,
            AuthorizationFormConsent.ConsentExpirationDateTime,
            AuthorizationFormConsent.ConsentGiverId,
            AuthorizationFormConsent.Knowledge__c,
            AuthorizationFormConsent.Name,
            AuthorizationFormConsent.Status,
            AuthorizationFormConsent.CreatedDate
        };
    }

    /**
     * @description Returns list of authorization form consents by id
     * @param idSet Set<Id> set of authorization form consent ids
	 *
     * @return list of selected Authorization Form Consents
     */
    public List<AuthorizationFormConsent> selectById(Set<Id> idSet) {
        return (List<AuthorizationFormConsent>) Database.query(
            newQueryFactory()
            .setCondition('Id IN :idSet')
            .toSOQL()
        );
    }

    /**
     * @description Select AuthorizationFormConsent filtered by contact ID
     * @param idSet set of Ids of contact
     *
     * @return list of AuthorizationFormConsent
     */
    public List<AuthorizationFormConsent> selectByContactId(Set<Id> idSet) {
        return (List<AuthorizationFormConsent>) Database.query(
            newQueryFactory()
            .setCondition('ConsentGiverId IN :idSet')
            .toSOQL()
        );
    }
}