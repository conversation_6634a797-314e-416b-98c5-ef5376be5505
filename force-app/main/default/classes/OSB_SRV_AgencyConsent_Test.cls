/**
 * @description Test class for AuthorizationFormConsent_CTRL
 *
 * <AUTHOR> (<EMAIL>)
 * @date October 2024
 * @UserStory SFP-36886
 */
@IsTest
public class OSB_SRV_AgencyConsent_Test {
    private static final String CONTACT_FIRST_NAME = 'userFirstName';
    private static final String CONTACT_LAST_NAME = 'userLastName';

    @TestSetup
    static void setupTestData() {
        AuthorizationForm authForm = new AuthorizationForm(
            Name = 'OneHub | Agency Disclaimer - Rev1',
            RevisionNumber = '1'
        );
        insert authForm;

        AuthorizationFormText authFormText = new AuthorizationFormText(
            AuthorizationFormId = authForm.Id,
            Name = 'Test Authorization Form Text',
            DetailAuthorizationFormText = 'Test details of the form',
            IsActive = true,
            Locale = 'en_ZA',
            TextVersion__c = 1
        );
        insert authFormText;
    }

    @IsTest
    static void testCreateConsentRecord() {
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        fflib_ISObjectUnitOfWork uowMock = new fflib_SObjectMocks.SObjectUnitOfWork(
            mocks
        );

        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(SEL_Contacts.class);

        ORG_Application.unitOfWork.setMock(uowMock);

        Contact testContact = (Contact) new BLD_Contact()
            .name(CONTACT_FIRST_NAME, CONTACT_LAST_NAME)
            .mock();

        Knowledge__kav article = (Knowledge__kav) new BLD_Knowledge()
        .setOSBData()
        .isComingSoon(false)
        .useSolution()
        .mock();

        mocks.startStubbing();
        mocks.when(contactsSel.sObjectType()).thenReturn(Contact.SObjectType);
        mocks.when(contactsSel.selectByUserId((Set<ID>) fflib_Match.anyObject())).thenReturn(new List<Contact> {testContact});
        mocks.stopStubbing();

        ORG_Application.selector.setMock(contactsSel);

        Map<String, Object> consentDetails = new Map<String, Object>{
            'authorizationFormTextId' => [SELECT Id FROM AuthorizationFormText LIMIT 1].Id,
            'knowledgeId' => article.Id,
            'knowledgeName' => article.Title
        };

        Test.startTest();
        OSB_SRV_AgencyConsent.createConsentRecord(consentDetails);
        Test.stopTest();

        fflib_ArgumentCaptor argument = fflib_ArgumentCaptor.forClass(
            fflib_ISObjectUnitOfWork.class
        );
        ((fflib_ISObjectUnitOfWork) mocks.verify(uowMock, 1))
            .registerNew((AuthorizationFormConsent) argument.capture());
        AuthorizationFormConsent createdConsent = (AuthorizationFormConsent) argument.getValue();
        
        Assert.areEqual(article.id, createdConsent.Knowledge__c, 'Consent Record Created with correct knowledge details.');
        Assert.areEqual(testContact.id, createdConsent.ConsentGiverId, 'Consent Record Created with correct Contact details.');
        Assert.areEqual(consentDetails.get('authorizationFormTextId'), createdConsent.authorizationFormTextId, 'Consent Record Created with correct Text details.');
    }
    
    @isTest
    static void testCreateCatchException(){
        fflib_ApexMocks mocks = new fflib_ApexMocks();
        fflib_ISObjectUnitOfWork uowMock = new fflib_SObjectMocks.SObjectUnitOfWork(
            mocks
        );

        SEL_Contacts contactsSel = (SEL_Contacts) mocks.mock(SEL_Contacts.class);

        ORG_Application.unitOfWork.setMock(uowMock);

        Contact testContact = (Contact) new BLD_Contact()
            .name(CONTACT_FIRST_NAME, CONTACT_LAST_NAME)
            .mock();

        Knowledge__kav article = (Knowledge__kav) new BLD_Knowledge()
        .setOSBData()
        .isComingSoon(false)
        .useSolution()
        .mock();

        Exception exceptionToThrow = new HandledException('Failed to create consent');
        mocks.startStubbing();
        mocks.when(contactsSel.sObjectType()).thenReturn(Contact.SObjectType);
        mocks.when(contactsSel.selectByUserId((Set<Id>) fflib_Match.anyObject())).thenThrow(exceptionToThrow);
        mocks.stopStubbing();

        ORG_Application.selector.setMock(contactsSel);

        Map<String, Object> consentDetails = new Map<String, Object>{
            'authorizationFormTextId' => null,
            'knowledgeId' => article.Id,
            'knowledgeName' => article.Title
        };

        Test.startTest();
        try{ 
            OSB_SRV_AgencyConsent.createConsentRecord(consentDetails);
        } catch(Exception e) {
            Assert.areEqual('Failed to create consent',e.getMessage(),'Consent creation exception thrown');
        } 
        Test.stopTest();
    }
}