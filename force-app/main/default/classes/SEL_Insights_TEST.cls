/**
 * @description test class for SEL_Insights class
 * <AUTHOR>
 * @UserStory SFP-25120
 * @date DEC 2023
 */
@IsTest(IsParallel=true)
public class SEL_Insights_TEST {
    @IsTest
    static void shouldGetInsightsByIdsd() {
        Test.startTest();
        new SEL_Insights().getInsightsByIds(new Set<String>());
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Id in :ids'), 'Condition used id set.');
    }

    @IsTest
    static void shouldGetInsightsWithDescendingOrder() {
        Test.startTest();
        new SEL_Insights().getInsightsWithDescendingOrder(null,new Set<Id>());
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQ<PERSON>yFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Persona__c !=\'Client\' AND Event_Date__c = LAST_N_DAYS:90 AND Opportunity__c = null AND Is_Expired__c=false AND Is_Snoozed__c = false AND Is_Provided_Feedback__c=false AND (OwnerId =:cstTeamMemberId OR Id IN : insightIds)'), 'Condition used user id set.');
    }
    
    @IsTest
    static void shouldGetInsightsOrderByOptimizationRank() {
        Test.startTest();
        new SEL_Insights().getInsightsOrderByOptimizationRank(null, new Set<Id>(),0);
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Persona__c =\'Client\' AND Event_Date__c = LAST_N_DAYS:90 AND Opportunity__c = null AND Is_Expired__c=false AND Is_Snoozed__c = false AND Is_Provided_Feedback__c=false AND (Id IN : insightIds)'), 'Condition used Persona, insight id set and not actioned nudges.');
    }
    
    @IsTest
    static void shouldSelectInsightsBySearchKey() {
        Test.startTest();
        new SEL_Insights().selectInsightsBySearchKey('test','test','test','test',true,true,true,true);
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Opportunity__c = null AND Status__c != \'Rejected\' AND Is_Expired__c=false'), 'Condition used to get the not actioned nudges.');
    }

    @IsTest
    static void shouldSelectWithoutCondition() {
        Test.startTest();
        new SEL_Insights().selectWithoutCondition();
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(null, result.getCondition(), 'No condition used.');
    }
    @IsTest
    static void shouldSelectInsightsByIdsWoSharing() {
        Test.startTest();
        new SEL_Insights().selectInsightsByIdsWoSharing(new Set<String>());
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Id in :ids'), 'Condition used insight id set.');
    }
    @IsTest
    static void shouldSelectAcitveInsightsByDateAndPersona() {
        Test.startTest();
        new SEL_Insights().selectAcitveInsightsByDateAndPersona(0,'Client',0);
        Test.stopTest();
        fflib_QueryFactory result = fflib_QueryFactory.lastQueryFactory;
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Event_Date__c = LAST_N_DAYS:0 AND Opportunity__c = null AND Is_Expired__c=false AND Is_Snoozed__c = false AND Is_Provided_Feedback__c=false AND Persona__c =: personaType'), 'Condition used Persona and not actioned nudges criteria.');
        new SEL_Insights().selectAcitveInsightsByDateAndPersona(0,'',0);
        Assert.areEqual(true, result.getCondition().containsIgnoreCase('Event_Date__c = LAST_N_DAYS:0 AND Opportunity__c = null AND Is_Expired__c=false AND Is_Snoozed__c = false AND Is_Provided_Feedback__c=false'), 'Condition used Persona as Client and not actioned nudges criteria.');
    }

}