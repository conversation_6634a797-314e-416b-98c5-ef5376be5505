/**
 * Controller class for OSBHeaderOPTL component
 * 
 * <AUTHOR> (w<PERSON><PERSON><PERSON><PERSON>@deloittece.com)
 * @date March 2020
 * 
 * @LastModified Dec 2023
 * <AUTHOR> (<EMAIL>)
 * @UserStory SFP-25120
 * @LastModifiedReason Added new method getting unread feed items number for logged in user
 * 
 * @LastModified January 2024
 * <AUTHOR> (<EMAIL>)
 * @UserStory SFP-45156
 * @LastModifiedReason Update to retrieve myDocuments URL
 */

public with sharing class OSB_Header_CTRL {
	private static final sbgplatform.rflib_Logger LOGGER = sbgplatform.rflib_LoggerUtil.getFactory().createLogger('OSB_Header_CTRL');

    public static final String ALL_USERS = 'All Users';
    public static final String PING_DIRECTORY = 'Ping_directory';
    public static final String LOGIN_URL = '/services/auth/sso/Ping_Authentication';
    public static final String IELOGIN_URL = '/s/unsupported-internet-explorer';

    private static final String TERMS_LINK_SEL = 'OSB_T&C';
    private static final String MY_DOCUMENTS_URL = 'OSB_My_Documents_URL';

    /**
    * Returns integer count for notification bell
    * 
    * @return List<object> feedItems unread
    */
    @AuraEnabled
    public static Map<String,Integer> getUnreadFeedItemsNumberForUser() {
        Map<String,Integer> unreadFeedItemsCountMap = new Map<String,Integer>();
        try{  
            String userId  = UserInfo.getUserId();
            Integer generalNotificationCount = 0;
            Boolean isSmartNudgeSubscribed = false;
            List<Notification__c> generalNotificationList = SEL_Notifications.newInstance().getFeedItemsForUser();
            if(!generalNotificationList.isEmpty()) {
                for(Notification__c notification : generalNotificationList){
                    if(notification.Is_Unread__c) {
                        generalNotificationCount = generalNotificationCount + 1;
                    }
                }
            }
            unreadFeedItemsCountMap.put('General',generalNotificationCount);
            List<User> userList = SEL_Users.newInstance().selectById(new Set<Id>{userId});
            List<Subscribed_Solutions__c> subscribedSolutionsList = SEL_SubscribedSolutions.newInstance().selectByContactId(new Set<Id>{ userList[0].ContactId });
            isSmartNudgeSubscribed = getUnreadFeedItemsNumberForUserHelper(subscribedSolutionsList);
           
            if(isSmartNudgeSubscribed) {
                List<Insight_Client_Relationship__c> insightClientRelationshipList = SEL_InsightClientRelationships.newInstance().selectByContactIdAndIsUnreadWoSharing(new Set<String>{userList[0].ContactId},true);
                unreadFeedItemsCountMap.put('SmartNudge',insightClientRelationshipList.size());
            } else {
                unreadFeedItemsCountMap.put('SmartNudge',0);  
            }
        } catch(Exception exp) {
            LOGGER.error('OSB_Header_CTRL:getUnreadFeedItemsNumberForUser Exception logged: ',exp);
            throw new AuraHandledException(exp.getMessage());
        }
        return unreadFeedItemsCountMap;
    }

    /**
    * Returns integer count for notification bell
    * @param subscribedSolutionsList List<Subscribed_Solutions__c>
    * @return true false if user is smart nudge subscribed 
    */
    private static Boolean getUnreadFeedItemsNumberForUserHelper(List<Subscribed_Solutions__c> subscribedSolutionsList) {
        Boolean isSmartNudgeSubscribed = false;
        
        if(!subscribedSolutionsList.isEmpty()){
            for(Subscribed_Solutions__c ss : subscribedSolutionsList){
                if(ss.Solution__r.Title == 'SmartNudge' && ss.Subscribed__c == 'Yes'){
                    isSmartNudgeSubscribed = true;
                }
            }
        }
        return isSmartNudgeSubscribed;
    }
    
    /**
    * Used to display the user name once logged in on dashboard
    * 
    * @return String FirstName of logged in User
    */ 
    @AuraEnabled(Cacheable=true)
    public static User getUserNameIfLoggedIn() {
        try {
            if(UTL_User.isLoggedInUser()) {
                return SEL_Users.newInstance().selectById(new Set<Id>{UserInfo.getUserId()})[0];
            }
        } catch(Exception exp){
            LOGGER.error('OSB_Header_CTRL:getUserNameIfLoggedIn Exception logged: ',exp);
            throw new AuraHandledException(exp.getMessage());
        }
        return null;
    }
    
    /**
    * Used to display team profile to the correct users
    * 
    * @return String OSB_Access_Role of logged in User
    */ 
    @AuraEnabled(Cacheable=true)
    public static String getContactAuth() {
        String loggedInUserRole;
        try{
            loggedInUserRole = SEL_Contacts.newInstance().selectByUserId(new Set<Id>{UserInfo.getUserId()})[0].OSB_Community_Access_Role__c;
        } catch(Exception exp){
            LOGGER.error('OSB_Header_CTRL:getContactAuth Exception logged: ',exp);
            throw new AuraHandledException(exp.getMessage());
        }
        return loggedInUserRole;
    }
    
    /**
    * Return the Change Password URL of loggen in User
    * 
    * @return String URL
    */ 
    @AuraEnabled(Cacheable=true)
    public static String getChangePasswordUrlPing() {
        try{  
            String userName;
            String adapterId = 'AdapterId';
            String targetResourceName = 'OSB_Ping_TargetResource';
            String pingUrlExtention = OSB_URLs__c.getValues(adapterId).Value__c;
            String targetResource = '&TargetResource=' + OSB_URLs__c.getValues(targetResourceName).Value__c;
            if (UTL_User.isLoggedInUser()) {
                userName = UserInfo.getUserEmail();
                String pingBaseUrl = [SELECT DeveloperName, Endpoint FROM NamedCredential where DeveloperName =:PING_DIRECTORY  LIMIT 1].Endpoint;
                return pingBaseUrl+pingUrlExtention+username+targetResource;
            }
        }catch(Exception exp){
            LOGGER.error('OSB_Header_CTRL:getChangePasswordUrlPing Exception logged: ',exp);
            throw new AuraHandledException(exp.getMessage());
        }
        return null;
    }
    
    /**
    * Return the Login URL
    * 
    * @return String URL
    */ 
    @AuraEnabled(Cacheable=true)
    public static String getLoginURL() {
        return LOGIN_URL;
    }

    /**
    * Return the Unsupported Browser Page url
    * To prevent users from logging in to OneHub using Internet Explorer as it is no longer supported.
    * @return String URL
    */ 
    @AuraEnabled(Cacheable=true)
    public static String getIELoginURL() {
        return IELOGIN_URL;
    }

    /**
    * Returns link for Terms and conditions
    * <br/>SGPRT-4230
    *
    * @return string value of T&C url
    */
    @AuraEnabled(Cacheable=true)
    public static String getTermsLink() {
        String osbURLs;
        try {
            osbURLs = OSB_URLs__c.getValues(TERMS_LINK_SEL).Value__c;
        } catch(Exception exp) {
            LOGGER.error('OSB_Header_CTRL:getTermsLink Exception logged: ',exp);
            throw new AuraHandledException(exp.getMessage());
        }
        return osbURLs;
    } 

    /**
    * Returns link for My Documents
    *
    * @return string value of My Documents url
    */
    @AuraEnabled(Cacheable=true)
    public static String getMyDocumentsLink() {
        try {
            return OSB_URLs__c.getValues(MY_DOCUMENTS_URL).Value__c;
        } catch (Exception e) {
            LOGGER.error('OSB_Header_CTRL : getMyDocumentsLink - Exception logged: ', e);
            throw new AuraHandledException(e.getMessage());
        }
    }

    /**
     * Returns url for selected name
     *
     * @param name name of the url
     */
    @AuraEnabled(Cacheable=true)
    public static String getURLByName(String name)
    {
        String osbURLs;

        try
        {
            osbURLs = OSB_URLs__c.getValues(name).Value__c;
        }
        catch(Exception exp)
        {
            LOGGER.error('OSB_Header_CTRL:getURLByName' + name + ' Exception logged: ',exp);
            throw new AuraHandledException(exp.getMessage());
        }

        return osbURLs;
    }

    /**
     * Returns list of urls for selected names
     *
     * @param names list of names
     */
    @AuraEnabled(Cacheable=true)
    public static List<String> getSelectedUrls(List<String> names)
    {
        List<String> urls = new List<String>();

        for(String name : names)
        {
            urls.add(OSB_URLs__c.getValues(name).Value__c);
        }

        return urls;
    }
}