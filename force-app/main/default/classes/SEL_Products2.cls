/**
 * Selector layer class for Product2 SObject
 *
 * <AUTHOR>
 * @date 2022-08-30
 */
public inherited sharing class SEL_Products2 extends fflib_SObjectSelector {
    /**
     * This is used to retrieve the sObject name when building the SOQL
     * queries.
     *
     * @return the SObject type for the selector.
     */
    public Schema.SObjectType getSObjectType() {
        return Product2.SObjectType;
    }

    /**
     * Creates a new instance of the selector via the application class. This is here to allow unit tests to override
     * and inject a mock instead of this class or to switch out this class for a new version.
     *
     * @return instance of the class
     */
    public static SEL_Products2 newInstance() {
        return (SEL_Products2) ORG_Application.selector.newInstance(
            Product2.SObjectType
        );
    }

    /**
     * This is used to retrieve a specific set of SObject fields
     *
     * @return List of SObjectField
     */
    public List<Schema.SObjectField> getSObjectFieldList() {
        return new List<Schema.SObjectField>{
            Product2.Id,
            Product2.Name,
            Product2.Grand_Parent_Product__c,
            Product2.Parent_Product__c,
            Product2.Product_Division__c
        };
    }
    /**
     * @description Returns CIB related AND User_Division related Products from Product2 object used on search operation on custom lwc component
     *
     * @param userDivs - User-division field
     * @return List of Product2 records
     */
    public List<Product2> selectCIBRelatedProducts2(String[] userDivs) {
        return (List<Product2>) Database.query(
            newQueryFactory()
                .setCondition(
                    'Grand_Parent_Product__c != null AND Product_Division__c != null AND Product_Division__c IN :userDivs'
                )
                .setOrdering(
                    'Grand_Parent_Product__c',
                    fflib_QueryFactory.SortOrder.ASCENDING,
                    true
                )
                .toSOQL()
        );
    }
}
